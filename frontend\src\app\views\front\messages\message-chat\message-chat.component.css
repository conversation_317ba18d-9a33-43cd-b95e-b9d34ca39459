/* ========================================
   THÈME MODERNE ET INNOVANT
   ======================================== */

:root {
  /* Couleurs principales modernes */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  /* Couleurs neutres modernes */
  --modern-white: #ffffff;
  --modern-light: #f8fafc;
  --modern-gray-100: #f1f5f9;
  --modern-gray-200: #e2e8f0;
  --modern-gray-300: #cbd5e1;
  --modern-gray-400: #94a3b8;
  --modern-gray-500: #64748b;
  --modern-gray-600: #475569;
  --modern-gray-700: #334155;
  --modern-gray-800: #1e293b;
  --modern-gray-900: #0f172a;

  /* Mode sombre moderne */
  --dark-primary: #1a1a2e;
  --dark-secondary: #16213e;
  --dark-accent: #0f3460;
  --dark-surface: #252545;
  --dark-text: #e2e8f0;

  /* Effets modernes */
  --glass-effect: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-modern: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
  --blur-effect: blur(20px);

  /* Couleurs fluo pour icônes en mode sombre */
  --neon-cyan: #00ffff;
  --neon-pink: #ff00ff;
  --neon-green: #00ff00;
  --neon-orange: #ff6600;
  --neon-purple: #9900ff;
  --neon-yellow: #ffff00;
  --neon-blue: #0099ff;
  --neon-red: #ff3366;

  /* Dégradés fluo */
  --neon-gradient-1: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
  --neon-gradient-2: linear-gradient(135deg, #00ff00 0%, #ffff00 100%);
  --neon-gradient-3: linear-gradient(135deg, #ff6600 0%, #ff3366 100%);
  --neon-gradient-4: linear-gradient(135deg, #9900ff 0%, #0099ff 100%);

  /* Effets de lueur fluo */
  --neon-glow-cyan: 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff;
  --neon-glow-pink: 0 0 20px #ff00ff, 0 0 40px #ff00ff, 0 0 60px #ff00ff;
  --neon-glow-green: 0 0 20px #00ff00, 0 0 40px #00ff00, 0 0 60px #00ff00;
  --neon-glow-orange: 0 0 20px #ff6600, 0 0 40px #ff6600, 0 0 60px #ff6600;
}

/* Styles pour les messages - Couleurs unifiées */
.futuristic-message-current-user .futuristic-message-bubble {
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.9),
    rgba(131, 56, 236, 0.9)
  );
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 247, 255, 0.4);
  backdrop-filter: blur(10px);
  transform: perspective(800px) rotateX(0deg);
  transition: all 0.3s ease;
}

.futuristic-message-current-user .futuristic-message-bubble:hover {
  transform: perspective(800px) rotateX(2deg) translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 247, 255, 0.5);
}

.futuristic-message-current-user .futuristic-message-bubble::before {
  border-left: 8px solid rgba(131, 56, 236, 0.8);
}

.futuristic-message-other-user .futuristic-message-bubble {
  background: rgba(30, 30, 40, 0.7);
  border-color: rgba(0, 247, 255, 0.15);
}

.futuristic-message-other-user .futuristic-message-bubble:hover {
  border-color: rgba(0, 247, 255, 0.25);
  box-shadow: 0 2px 15px rgba(0, 247, 255, 0.2);
}

/* En-tête moderne et attractif */
.whatsapp-chat-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
  border-bottom: 1px solid var(--glass-border);
  height: 60px;
  backdrop-filter: var(--blur-effect);
  box-shadow: var(--shadow-modern);
  position: relative;
  overflow: hidden;
}

/* Animation de dégradé pour l'en-tête */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Effet de particules dans l'en-tête */
.whatsapp-chat-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 50%
    );
  animation: particleFloat 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes particleFloat {
  0%,
  100% {
    opacity: 0.3;
    transform: translateY(0px);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-5px);
  }
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--dark-secondary);
  background-image: var(--accent-gradient);
  background-size: 200% 200%;
  border-bottom: 1px solid var(--dark-accent);
}

.whatsapp-user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}

.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #25d366;
  border: 2px solid #f0f2f5;
}

:host-context(.dark) .whatsapp-online-indicator {
  border-color: #2a2a2a;
}

.whatsapp-user-details {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
}

.whatsapp-username {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333;
}

:host-context(.dark) .whatsapp-username {
  color: #e0e0e0;
}

.whatsapp-status {
  font-size: 0.75rem;
  color: #666;
}

:host-context(.dark) .whatsapp-status {
  color: #aaa;
}

.whatsapp-actions {
  display: flex;
  gap: 16px;
}

/* Boutons d'action modernes avec effets fluo futuristes */
.whatsapp-action-button {
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.1) 0%,
    rgba(255, 20, 147, 0.1) 50%,
    rgba(57, 255, 20, 0.1) 100%
  );
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(0, 247, 255, 0.3);
  color: #00f7ff;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Effet de lueur futuriste au survol */
.whatsapp-action-button::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(0, 247, 255, 0.4) 90deg,
    rgba(255, 20, 147, 0.4) 180deg,
    rgba(57, 255, 20, 0.4) 270deg,
    transparent 360deg
  );
  opacity: 0;
  transition: all 0.4s ease;
  animation: rotateHalo 3s linear infinite;
}

.whatsapp-action-button:hover::before {
  opacity: 1;
}

.whatsapp-action-button:hover {
  transform: scale(1.15) translateY(-3px);
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.6), 0 0 60px rgba(255, 20, 147, 0.3),
    0 10px 30px rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 247, 255, 0.8);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.8);
}

/* Mode sombre avec couleurs fluo */
:host-context(.dark) .whatsapp-action-button {
  background: var(--glass-effect);
  border: 1px solid var(--neon-cyan);
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

:host-context(.dark) .whatsapp-action-button:hover {
  background: var(--neon-gradient-1);
  color: white;
  text-shadow: none;
  box-shadow: var(--neon-glow-cyan);
  border-color: transparent;
}

/* Icônes spécifiques avec couleurs fluo différentes */
:host-context(.dark) .whatsapp-action-button:nth-child(1) {
  border-color: var(--neon-pink);
  color: var(--neon-pink);
  text-shadow: 0 0 10px var(--neon-pink);
  box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
}

:host-context(.dark) .whatsapp-action-button:nth-child(1):hover {
  background: var(--neon-gradient-3);
  box-shadow: var(--neon-glow-pink);
}

:host-context(.dark) .whatsapp-action-button:nth-child(2) {
  border-color: var(--neon-green);
  color: var(--neon-green);
  text-shadow: 0 0 10px var(--neon-green);
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
}

:host-context(.dark) .whatsapp-action-button:nth-child(2):hover {
  background: var(--neon-gradient-2);
  box-shadow: var(--neon-glow-green);
}

:host-context(.dark) .whatsapp-action-button:nth-child(3) {
  border-color: var(--neon-orange);
  color: var(--neon-orange);
  text-shadow: 0 0 10px var(--neon-orange);
  box-shadow: 0 0 20px rgba(255, 102, 0, 0.3);
}

:host-context(.dark) .whatsapp-action-button:nth-child(3):hover {
  background: var(--neon-gradient-4);
  box-shadow: var(--neon-glow-orange);
}

/* ========================================
   ICÔNES FUTURISTES MODERNES AVEC EFFETS HOLOGRAPHIQUES
   ======================================== */

/* Animations pour les effets futuristes */
@keyframes pulseGreen {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(57, 255, 20, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(57, 255, 20, 0.7);
  }
}

@keyframes pulseMagenta {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(255, 20, 147, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 20, 147, 0.7);
  }
}

@keyframes pulseOrange {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 165, 0, 0.7);
  }
}

@keyframes rotateHalo {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.8;
  }
}

/* ========================================
   ANCIENS SÉLECTEURS :has() SUPPRIMÉS - REMPLACÉS PAR LES CLASSES SPÉCIFIQUES
   ======================================== */

/* Ces styles ont été remplacés par les classes .btn-* plus bas dans le fichier
   pour assurer la compatibilité avec tous les navigateurs */

/* Conteneur de messages moderne avec fond attractif - CONSOLIDÉ PLUS BAS */

/* Mode sombre pour le conteneur de messages */
:host-context(.dark) .futuristic-messages-container {
  background: var(--dark-primary);
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(102, 126, 234, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(245, 87, 108, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 50% 50%,
      rgba(79, 172, 254, 0.08) 0%,
      transparent 50%
    ),
    linear-gradient(
      135deg,
      rgba(26, 26, 46, 0.8) 0%,
      rgba(22, 33, 62, 0.8) 100%
    );
}

/* Animation de scan pour le mode sombre */
@keyframes scan {
  0% {
    top: 0;
  }
  100% {
    top: 100%;
  }
}

/* Style pour les scrollbars futuristes */
.futuristic-messages-container::-webkit-scrollbar {
  width: 5px;
}

.futuristic-messages-container::-webkit-scrollbar-track {
  background: rgba(0, 247, 255, 0.05);
}

.futuristic-messages-container::-webkit-scrollbar-thumb {
  background-color: var(--accent-color);
  border-radius: 10px;
  border: transparent;
}

/* Wrapper de message futuriste */
.futuristic-message-wrapper {
  margin-bottom: 4px;
  position: relative;
  z-index: 1;
}

/* Message futuriste style WhatsApp */
.futuristic-message {
  display: flex;
  align-items: flex-end;
  margin-bottom: 1px;
  position: relative;
  width: 100%;
}

/* Bulle de message futuriste style WhatsApp amélioré */
.futuristic-message-bubble {
  margin-bottom: 0.25rem;
  max-width: 70%;
  min-width: 40px;
  padding: 0.6rem 0.8rem;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: normal;
  display: inline-block;
  width: auto;
  white-space: normal;
  overflow-wrap: break-word;
  text-align: left;
  direction: ltr;
  position: relative;
  transition: all var(--transition-fast);
  animation: fadeIn 0.3s ease-out;
  border-radius: 12px;
  letter-spacing: 0.02em;
  font-weight: 400;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

/* Style pour le contenu du message */
.futuristic-message-content {
  max-width: 80%;
}

.futuristic-message-text {
  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
  text-align: left;
  direction: ltr;
  min-width: 80px;
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 1.5;
}

.futuristic-message-current-user .futuristic-message-text {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.futuristic-message-other-user .futuristic-message-text {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

/* Séparateur de date futuriste */
.futuristic-date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.5rem 0;
  color: var(--text-dim);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.futuristic-date-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    var(--accent-color),
    transparent
  );
  opacity: 0.3;
}

.futuristic-date-text {
  margin: 0 10px;
  padding: 2px 8px;
  background-color: rgba(0, 247, 255, 0.05);
  border-radius: var(--border-radius-sm);
}

/* Heure du message futuriste */
.futuristic-message-time {
  font-size: 0.7rem;
  margin-top: 0.2rem;
  opacity: 0.7;
  color: var(--text-dim);
}

/* Informations du message futuriste */
.futuristic-message-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
  margin-top: 4px;
  font-size: 0.75rem;
  letter-spacing: 0.02em;
  font-weight: 300;
}

.futuristic-message-current-user .futuristic-message-info {
  color: rgba(255, 255, 255, 0.8);
}

.futuristic-message-other-user .futuristic-message-info {
  color: rgba(0, 247, 255, 0.7);
}

.futuristic-message-status {
  color: rgba(0, 247, 255, 0.9);
}

/* Style pour les messages de l'utilisateur actuel (style WhatsApp) */
.futuristic-message-current-user {
  justify-content: flex-end;
  width: 100%;
  display: flex;
  margin-left: auto;
  animation: slideInRight 0.3s ease-out;
}

.futuristic-message-current-user .futuristic-message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-width: 80%;
  margin-left: auto;
}

.futuristic-message-current-user .futuristic-message-bubble {
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
  color: white;
  border-radius: 18px 18px 4px 18px;
  margin-left: auto;
  box-shadow: var(--shadow-modern);
  position: relative;
  border: 1px solid var(--glass-border);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  backdrop-filter: var(--blur-effect);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Flèche style WhatsApp pour les messages de l'utilisateur actuel */
.futuristic-message-current-user .futuristic-message-bubble::before {
  content: "";
  position: absolute;
  top: 0;
  right: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-left: 8px solid rgba(131, 56, 236, 0.8);
  border-bottom: 8px solid transparent;
  z-index: 1;
  filter: drop-shadow(2px 0px 2px rgba(0, 0, 0, 0.1));
}

.futuristic-message-current-user .futuristic-message-bubble:hover {
  box-shadow: var(--glow-effect);
}

/* Style pour les messages des autres utilisateurs (style WhatsApp) */
.futuristic-message-other-user {
  justify-content: flex-start;
  width: 100%;
  display: flex;
  margin-right: auto;
  animation: slideInLeft 0.3s ease-out;
}

.futuristic-message-other-user .futuristic-message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 80%;
  margin-right: auto;
}

/* Style pour les messages des autres utilisateurs en mode clair */
:host-context(:not(.dark))
  .futuristic-message-other-user
  .futuristic-message-bubble {
  background: var(--modern-white);
  background-image: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(245, 87, 108, 0.05) 100%
  );
  color: var(--modern-gray-800);
  border-radius: 18px 18px 18px 4px;
  margin-right: auto;
  box-shadow: var(--shadow-modern);
  border: 1px solid var(--modern-gray-200);
  position: relative;
  backdrop-filter: var(--blur-effect);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Style pour les messages des autres utilisateurs en mode sombre */
:host-context(.dark) .futuristic-message-other-user .futuristic-message-bubble {
  background: var(--dark-surface);
  background-image: linear-gradient(
    135deg,
    rgba(79, 172, 254, 0.1) 0%,
    rgba(245, 87, 108, 0.1) 100%
  );
  color: var(--dark-text);
  border-radius: 18px 18px 18px 4px;
  margin-right: auto;
  box-shadow: var(--shadow-modern);
  border: 1px solid var(--dark-accent);
  position: relative;
  backdrop-filter: var(--blur-effect);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Flèche style WhatsApp pour les messages des autres utilisateurs - Mode clair */
:host-context(:not(.dark))
  .futuristic-message-other-user
  .futuristic-message-bubble::before {
  content: "";
  position: absolute;
  top: 0;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-right: 8px solid rgba(220, 225, 235, 0.8);
  border-bottom: 8px solid transparent;
  z-index: 1;
  filter: drop-shadow(-2px 0px 2px rgba(0, 0, 0, 0.05));
}

/* Flèche style WhatsApp pour les messages des autres utilisateurs - Mode sombre */
:host-context(.dark)
  .futuristic-message-other-user
  .futuristic-message-bubble::before {
  content: "";
  position: absolute;
  top: 0;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-right: 8px solid rgba(30, 30, 40, 0.7);
  border-bottom: 8px solid transparent;
  z-index: 1;
  filter: drop-shadow(-2px 0px 2px rgba(0, 0, 0, 0.1));
}

/* Hover pour les messages des autres utilisateurs - Mode clair */
:host-context(:not(.dark))
  .futuristic-message-other-user
  .futuristic-message-bubble:hover {
  background: rgba(230, 235, 245, 0.9);
  box-shadow: 0 6px 20px rgba(79, 95, 173, 0.2);
  border-color: rgba(79, 95, 173, 0.35);
  transform: perspective(800px) rotateX(2deg) translateY(-2px);
}

/* Hover pour les messages des autres utilisateurs - Mode sombre */
:host-context(.dark)
  .futuristic-message-other-user
  .futuristic-message-bubble:hover {
  background: rgba(40, 40, 50, 0.9);
  box-shadow: 0 6px 20px rgba(0, 247, 255, 0.3);
  border-color: rgba(0, 247, 255, 0.35);
  transform: perspective(800px) rotateX(2deg) translateY(-2px);
}

/* Zone de saisie futuriste */
/* Conteneur d'entrée avec effet de portail magique */
.futuristic-input-container {
  padding: 6px 10px;
  background-color: var(--medium-bg);
  min-height: 50px; /* Hauteur réduite */
  position: relative;
  z-index: 10;
  overflow: hidden;
}

/* Effet de bordure magique */
.futuristic-input-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 247, 255, 0.2) 20%,
    rgba(0, 247, 255, 0.8) 50%,
    rgba(0, 247, 255, 0.2) 80%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: borderFlow 3s infinite linear;
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);
  z-index: 1;
}

/* Effet de lueur ambiante */
.futuristic-input-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 247, 255, 0.05) 0%,
    transparent 70%
  );
  opacity: 0;
  animation: ambientGlow 4s infinite alternate;
  pointer-events: none;
  z-index: -1;
}

/* Animation de flux de bordure */
@keyframes borderFlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

/* Animation de lueur ambiante */
@keyframes ambientGlow {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 0.5;
    transform: scale(1.05);
  }
}

.futuristic-input-form {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  z-index: 2;
}

.futuristic-input-tools {
  display: flex;
  gap: 8px;
}

.futuristic-tool-button {
  background-color: rgba(0, 247, 255, 0.1);
  color: var(--accent-color);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.futuristic-tool-button:hover {
  background-color: rgba(0, 247, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--glow-effect);
}

.futuristic-tool-button.active {
  background-color: var(--accent-color);
  color: var(--dark-bg);
  animation: pulse 1.5s infinite;
}

/* Champ de saisie simplifié */
.futuristic-input-field {
  flex: 1;
  font-size: 0.9rem;
  padding: 10px 16px;
  height: 44px;
  border-radius: 22px;
  border: 1px solid rgba(0, 247, 255, 0.2);
  background-color: rgba(0, 247, 255, 0.05);
  color: var(--text-light);
  transition: all 0.3s ease;
}

/* Bouton d'envoi avec effet de portail magique */
.futuristic-send-button {
  position: relative;
  background: linear-gradient(135deg, #00f7ff, #0066ff);
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.5), 0 0 20px rgba(0, 247, 255, 0.2),
    inset 0 0 5px rgba(255, 255, 255, 0.3);
  overflow: hidden;
  z-index: 2;
}

/* Effet de halo lumineux */
.futuristic-send-button::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: conic-gradient(
    from 0deg,
    transparent 0%,
    rgba(0, 247, 255, 0.8) 25%,
    rgba(131, 56, 236, 0.8) 50%,
    rgba(0, 247, 255, 0.8) 75%,
    transparent 100%
  );
  border-radius: 50%;
  animation: rotateHalo 3s linear infinite;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Effet de particules internes */
.futuristic-send-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(0, 247, 255, 0.5) 30%,
    transparent 70%
  );
  border-radius: 50%;
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.3s ease;
  z-index: -1;
}

/* Animation de rotation du halo */
@keyframes rotateHalo {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animation de pulsation */
@keyframes pulseButton {
  0% {
    box-shadow: 0 0 10px rgba(0, 247, 255, 0.5), 0 0 20px rgba(0, 247, 255, 0.2);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 15px rgba(0, 247, 255, 0.7), 0 0 30px rgba(0, 247, 255, 0.4);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 10px rgba(0, 247, 255, 0.5), 0 0 20px rgba(0, 247, 255, 0.2);
    transform: scale(1);
  }
}

.futuristic-send-button:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7), 0 0 30px rgba(0, 247, 255, 0.4),
    inset 0 0 10px rgba(255, 255, 255, 0.5);
  animation: pulseButton 1.5s infinite;
}

.futuristic-send-button:hover::before {
  opacity: 1;
}

.futuristic-send-button:hover::after {
  opacity: 0.8;
  transform: scale(1.2);
}

.futuristic-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  animation: none;
}

.futuristic-send-button:disabled::before,
.futuristic-send-button:disabled::after {
  opacity: 0;
}

.message-header {
  padding: 0.25rem 0.5rem;
  border-bottom: 1px solid #e9ecef;
  height: 40px;
}

.message-header h5 {
  font-size: 0.8rem;
  margin-bottom: 0;
}

/* Style du conteneur principal moderne */
.futuristic-chat-container {
  width: 100%;
  margin: 0 auto;
  border: 1px solid var(--modern-gray-200);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-modern);
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--modern-white);
  background-image: radial-gradient(
      circle at 20% 20%,
      rgba(102, 126, 234, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(245, 87, 108, 0.03) 0%,
      transparent 50%
    ),
    linear-gradient(
      135deg,
      rgba(79, 172, 254, 0.01) 0%,
      rgba(118, 75, 162, 0.01) 100%
    );
  position: relative;
}

:host-context(.dark) .futuristic-chat-container {
  background: var(--dark-primary);
  background-image: radial-gradient(
      circle at 20% 20%,
      rgba(102, 126, 234, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(245, 87, 108, 0.08) 0%,
      transparent 50%
    ),
    linear-gradient(
      135deg,
      rgba(79, 172, 254, 0.05) 0%,
      rgba(118, 75, 162, 0.05) 100%
    );
  border: 1px solid var(--dark-accent);
}

/* Effet de particules flottantes dans le conteneur */
.futuristic-chat-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 10% 90%,
      rgba(102, 126, 234, 0.05) 0%,
      transparent 30%
    ),
    radial-gradient(
      circle at 90% 10%,
      rgba(245, 87, 108, 0.05) 0%,
      transparent 30%
    ),
    radial-gradient(
      circle at 50% 50%,
      rgba(79, 172, 254, 0.03) 0%,
      transparent 40%
    );
  animation: floatingParticles 12s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes floatingParticles {
  0%,
  100% {
    opacity: 0.3;
    transform: translateY(0px) scale(1);
  }
  33% {
    opacity: 0.6;
    transform: translateY(-10px) scale(1.05);
  }
  66% {
    opacity: 0.4;
    transform: translateY(-5px) scale(0.95);
  }
}

.message-date-divider {
  font-size: 0.75rem;
  color: #6c757d;
  margin: 0.5rem 0;
  text-align: center;
}

/* Images style Messenger dans les messages */
.futuristic-message-image-container {
  margin: 2px 0;
  overflow: hidden;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  position: relative;
  max-width: 220px;
  transform: perspective(800px) rotateX(0deg);
  transition: all 0.3s ease;
}

.futuristic-message-image-container:hover {
  transform: perspective(800px) rotateX(2deg) translateY(-3px);
}

.futuristic-image-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  transition: all var(--transition-fast);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  transform: perspective(800px) rotateX(0deg);
}

.futuristic-message-current-user .futuristic-image-wrapper {
  position: relative;
}

.futuristic-message-current-user .futuristic-image-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  right: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-left: 8px solid var(--secondary-color);
  border-bottom: 8px solid transparent;
  z-index: 1;
}

.futuristic-message-other-user .futuristic-image-wrapper {
  position: relative;
}

.futuristic-message-other-user .futuristic-image-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-right: 8px solid rgba(255, 255, 255, 0.1);
  border-bottom: 8px solid transparent;
  z-index: 1;
}

.futuristic-message-image-link {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  cursor: pointer;
}

.futuristic-message-image {
  width: 100%;
  display: block;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.futuristic-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-size: 1.5rem;
}

.futuristic-image-wrapper:hover .futuristic-message-image {
  transform: scale(1.05);
}

.futuristic-image-wrapper:hover {
  box-shadow: 0 12px 30px rgba(0, 247, 255, 0.4);
  transform: perspective(800px) rotateX(3deg) translateY(-5px);
  border-color: rgba(0, 247, 255, 0.4);
}

.futuristic-image-wrapper:hover .futuristic-image-overlay {
  opacity: 1;
}

.futuristic-message-current-user .futuristic-image-wrapper {
  border: 2px solid transparent;
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.8),
    rgba(131, 56, 236, 0.8)
  );
  padding: 2px;
  box-shadow: 0 5px 20px rgba(0, 247, 255, 0.3);
}

.futuristic-message-other-user .futuristic-image-wrapper {
  border: 2px solid rgba(0, 247, 255, 0.15);
  background-color: rgba(30, 30, 40, 0.7);
  padding: 2px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Dialog d'image en plein écran */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Modal d'image futuriste */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Styles pour le conteneur d'image en plein écran */
.fullscreen-image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 2147483647; /* Valeur maximale possible pour z-index */
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;
}

.fullscreen-image-wrapper {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.fullscreen-image-wrapper img {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.3);
}

/* Zone de saisie moderne - CONSOLIDÉ PLUS BAS */

/* Effet de lueur dans la zone de saisie */
.whatsapp-input-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.whatsapp-input-container:focus-within::before {
  opacity: 1;
}

:host-context(.dark) .whatsapp-input-container {
  background: var(--dark-secondary);
  background-image: linear-gradient(
      135deg,
      rgba(79, 172, 254, 0.08) 0%,
      rgba(245, 87, 108, 0.08) 100%
    ),
    radial-gradient(
      circle at 30% 70%,
      rgba(102, 126, 234, 0.1) 0%,
      transparent 50%
    );
  border-top: 1px solid var(--dark-accent);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
}

/* Formulaire d'entrée style WhatsApp */
.whatsapp-input-form {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* Outils d'entrée style WhatsApp */
.whatsapp-input-tools {
  display: flex;
  gap: 8px;
  margin-right: 8px;
}

/* Boutons d'outils modernes avec effets fluo futuristes */
.whatsapp-tool-button {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.15) 0%,
    rgba(255, 20, 147, 0.15) 50%,
    rgba(57, 255, 20, 0.15) 100%
  );
  backdrop-filter: blur(15px) saturate(150%);
  border: 1px solid rgba(0, 247, 255, 0.4);
  color: #00f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-size: 1rem;
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Effet de lueur au survol */
.whatsapp-tool-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.whatsapp-tool-button:hover::before {
  opacity: 1;
}

/* Mode sombre avec couleurs fluo */
:host-context(.dark) .whatsapp-tool-button {
  background: var(--glass-effect);
  border: 1px solid var(--neon-purple);
  color: var(--neon-purple);
  text-shadow: 0 0 8px var(--neon-purple);
  box-shadow: 0 0 15px rgba(153, 0, 255, 0.2);
}

/* Effet de survol pour les boutons d'outils */
.whatsapp-tool-button:hover,
.whatsapp-tool-button.active {
  transform: scale(1.1) translateY(-2px);
  box-shadow: var(--shadow-hover);
}

:host-context(.dark) .whatsapp-tool-button:hover,
:host-context(.dark) .whatsapp-tool-button.active {
  background: var(--neon-gradient-4);
  color: white;
  text-shadow: none;
  box-shadow: 0 0 25px var(--neon-purple);
  border-color: transparent;
}

/* Couleurs spécifiques pour chaque bouton d'outil */
:host-context(.dark) .whatsapp-tool-button:nth-child(1) {
  border-color: var(--neon-blue);
  color: var(--neon-blue);
  text-shadow: 0 0 8px var(--neon-blue);
  box-shadow: 0 0 15px rgba(0, 153, 255, 0.2);
}

:host-context(.dark) .whatsapp-tool-button:nth-child(1):hover {
  background: var(--neon-gradient-1);
  box-shadow: 0 0 25px var(--neon-blue);
}

:host-context(.dark) .whatsapp-tool-button:nth-child(2) {
  border-color: var(--neon-yellow);
  color: var(--neon-yellow);
  text-shadow: 0 0 8px var(--neon-yellow);
  box-shadow: 0 0 15px rgba(255, 255, 0, 0.2);
}

:host-context(.dark) .whatsapp-tool-button:nth-child(2):hover {
  background: var(--neon-gradient-2);
  box-shadow: 0 0 25px var(--neon-yellow);
}

/* Champ de saisie style WhatsApp */
.whatsapp-input-field {
  flex: 1;
  background-color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  color: #333;
  font-size: 0.9375rem;
  transition: all 0.2s ease;
  outline: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

:host-context(.dark) .whatsapp-input-field {
  background-color: #3a3a3a;
  color: #e0e0e0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Effet de focus pour le champ de saisie */
.whatsapp-input-field:focus {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Bouton d'envoi moderne avec effet laser vert néon */
.whatsapp-send-button {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(57, 255, 20, 0.9) 0%,
    rgba(0, 255, 127, 0.9) 50%,
    rgba(34, 197, 94, 0.9) 100%
  );
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
  border: 2px solid rgba(57, 255, 20, 0.7);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 25px rgba(57, 255, 20, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  font-size: 1.2rem;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

/* Effet de lueur pour le bouton d'envoi */
.whatsapp-send-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.whatsapp-send-button:hover::before {
  opacity: 1;
}

/* Effet de survol futuriste pour le bouton d'envoi */
.whatsapp-send-button:hover:not(:disabled) {
  transform: scale(1.15) translateY(-3px);
  box-shadow: 0 0 40px rgba(57, 255, 20, 0.8), 0 0 80px rgba(57, 255, 20, 0.4),
    0 10px 30px rgba(0, 0, 0, 0.2), inset 0 2px 0 rgba(255, 255, 255, 0.4);
  border-color: rgba(57, 255, 20, 1);
  animation: pulseGreen 0.6s ease-in-out;
  text-shadow: 0 0 15px rgba(255, 255, 255, 1);
}

/* Style pour le bouton d'envoi désactivé */
.whatsapp-send-button:disabled {
  background: var(--modern-gray-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  animation: none;
}

/* Bouton d'enregistrement vocal avec effet plasma rouge-orange */
.whatsapp-voice-button {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(255, 69, 0, 0.9) 0%,
    rgba(255, 20, 147, 0.9) 50%,
    rgba(220, 38, 127, 0.9) 100%
  );
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  border: 2px solid rgba(255, 69, 0, 0.7);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 25px rgba(255, 69, 0, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

/* Effet de lueur pour le bouton vocal */
.whatsapp-voice-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.whatsapp-voice-button:hover::before {
  opacity: 1;
}

/* Effet de survol futuriste pour le bouton vocal */
.whatsapp-voice-button:hover {
  transform: scale(1.15) translateY(-3px);
  box-shadow: 0 0 40px rgba(255, 69, 0, 0.8), 0 0 80px rgba(255, 20, 147, 0.4),
    0 10px 30px rgba(0, 0, 0, 0.2), inset 0 2px 0 rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 69, 0, 1);
  animation: pulseMagenta 0.6s ease-in-out;
  text-shadow: 0 0 15px rgba(255, 255, 255, 1);
}

/* État d'enregistrement actif avec pulsation continue */
.whatsapp-voice-button.recording {
  animation: pulseMagenta 1s ease-in-out infinite;
  box-shadow: 0 0 30px rgba(255, 69, 0, 0.9), 0 0 60px rgba(255, 20, 147, 0.6);
  border-color: rgba(255, 20, 147, 1);
}

/* Mode sombre avec couleurs fluo */
:host-context(.dark) .whatsapp-voice-button {
  background: var(--neon-gradient-3);
  background-size: 200% 200%;
  border: 1px solid var(--neon-red);
  box-shadow: 0 0 20px rgba(255, 51, 102, 0.4);
}

:host-context(.dark) .whatsapp-voice-button:hover {
  box-shadow: var(--neon-glow-orange);
  border-color: var(--neon-orange);
}

/* Aperçu de fichier style WhatsApp */
.whatsapp-file-preview {
  position: relative;
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  max-width: 200px;
  max-height: 200px;
}

.whatsapp-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-remove-button {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.whatsapp-remove-button:hover {
  background-color: rgba(255, 0, 0, 0.7);
}

/* Sélecteur d'émojis style WhatsApp */
.whatsapp-emoji-picker {
  position: absolute;
  bottom: 60px;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  flex-direction: column;
  height: 250px;
  overflow: hidden;
}

:host-context(.dark) .whatsapp-emoji-picker {
  background-color: #2a2a2a;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.whatsapp-emoji-categories {
  display: flex;
  padding: 8px;
  border-bottom: 1px solid #e0e0e0;
  overflow-x: auto;
  scrollbar-width: none;
}

:host-context(.dark) .whatsapp-emoji-categories {
  border-bottom: 1px solid #3a3a3a;
}

.whatsapp-emoji-categories::-webkit-scrollbar {
  display: none;
}

.whatsapp-emoji-category {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  color: #54656f;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

:host-context(.dark) .whatsapp-emoji-category {
  color: #aaa;
}

.whatsapp-emoji-category:hover,
.whatsapp-emoji-category.active {
  background-color: rgba(0, 0, 0, 0.05);
  color: #128c7e;
}

:host-context(.dark) .whatsapp-emoji-category:hover,
:host-context(.dark) .whatsapp-emoji-category.active {
  background-color: rgba(255, 255, 255, 0.1);
  color: #25d366;
}

.whatsapp-emoji-list {
  flex: 1;
  padding: 8px;
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  overflow-y: auto;
}

.whatsapp-emoji-item {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  background-color: transparent;
  border: none;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.whatsapp-emoji-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

:host-context(.dark) .whatsapp-emoji-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Styles pour le modal d'appel entrant */
.whatsapp-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-in-out;
}

.whatsapp-call-modal-content {
  background-color: #fff;
  border-radius: 12px;
  width: 300px;
  max-width: 90%;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

:host-context(.dark) .whatsapp-call-modal-content {
  background-color: #2a2a2a;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.whatsapp-call-header {
  padding: 20px;
  text-align: center;
}

.whatsapp-call-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 15px;
  border: 3px solid #25d366;
  animation: pulse 1.5s infinite;
}

.whatsapp-call-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-call-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: #333;
}

:host-context(.dark) .whatsapp-call-name {
  color: #e0e0e0;
}

.whatsapp-call-status {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0;
}

:host-context(.dark) .whatsapp-call-status {
  color: #aaa;
}

.whatsapp-call-actions {
  display: flex;
  border-top: 1px solid #e0e0e0;
}

:host-context(.dark) .whatsapp-call-actions {
  border-top: 1px solid #3a3a3a;
}

.whatsapp-call-reject,
.whatsapp-call-accept {
  flex: 1;
  padding: 15px;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.whatsapp-call-reject {
  background-color: #f44336;
  color: white;
}

.whatsapp-call-accept {
  background-color: #25d366;
  color: white;
}

.whatsapp-call-reject:hover {
  background-color: #d32f2f;
}

.whatsapp-call-accept:hover {
  background-color: #1da856;
}

.whatsapp-call-reject i,
.whatsapp-call-accept i {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.close-fullscreen-btn {
  position: absolute;
  top: -40px;
  right: -40px;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-fullscreen-btn:hover {
  background-color: rgba(0, 247, 255, 0.5);
  transform: scale(1.1);
}

.image-fullscreen-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.image-fullscreen-container img {
  max-width: 85%;
  max-height: 85%;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: 0 0 50px rgba(0, 247, 255, 0.3);
  animation: scaleIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(0, 247, 255, 0.2);
}

.image-fullscreen-dialog .close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  color: rgba(0, 247, 255, 0.9);
  border: 2px solid rgba(0, 247, 255, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 10000000;
  box-shadow: 0 0 20px rgba(0, 247, 255, 0.2);
}

.image-fullscreen-dialog .close-button:hover {
  background: rgba(0, 247, 255, 0.3);
  transform: scale(1.15) rotate(90deg);
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.4);
}

/* États de chargement futuristes */
.futuristic-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.futuristic-loading-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid rgba(0, 247, 255, 0.1);
  border-top-color: var(--accent-color);
  animation: spin 1.5s linear infinite;
  margin-bottom: 15px;
}

.futuristic-loading-text {
  color: var(--accent-color);
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.futuristic-loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background-color: rgba(0, 247, 255, 0.05);
  border-radius: var(--border-radius-md);
}

.futuristic-loading-dots {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
}

.futuristic-loading-dot {
  width: 8px;
  height: 8px;
  background-color: var(--accent-color);
  border-radius: 50%;
  animation: pulse 1.5s infinite ease-in-out;
}

/* Indicateur de début de conversation futuriste */
.futuristic-conversation-start {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 20px 0;
}

.futuristic-conversation-start-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    var(--accent-color),
    transparent
  );
  opacity: 0.3;
}

.futuristic-conversation-start-text {
  margin: 0 10px;
  padding: 4px 12px;
  background-color: rgba(0, 247, 255, 0.1);
  border-radius: var(--border-radius-md);
  color: var(--accent-color);
  font-size: 0.75rem;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* État d'erreur futuriste */
.futuristic-error-container {
  margin: 15px;
  padding: 15px;
  background: rgba(255, 0, 0, 0.1);
  border-left: 3px solid #ff3b30;
  border-radius: 5px;
  display: flex;
  align-items: flex-start;
}

.futuristic-error-icon {
  color: #ff3b30;
  font-size: 1.25rem;
  margin-right: 15px;
}

.futuristic-error-title {
  color: #ff3b30;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.futuristic-error-message {
  color: var(--text-dim);
  font-size: 0.8125rem;
}

/* Style pour les messages en attente d'envoi */
.futuristic-message-pending {
  opacity: 0.7;
}

/* Animation pour les messages en cours d'envoi */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 0.6;
  }
}

.futuristic-message-sending {
  animation: pulse 1.5s infinite ease-in-out;
}

/* Style pour les messages avec erreur */
.futuristic-message-error {
  border: 1px solid rgba(239, 68, 68, 0.5);
}

/* Messages vocaux futuristes */
.futuristic-voice-message-container {
  padding: 8px !important;
  min-width: 200px;
  border-radius: 14px;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transform: perspective(800px) rotateX(0deg);
  transition: all 0.3s ease;
}

.futuristic-voice-message-container:hover {
  transform: perspective(800px) rotateX(2deg) translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

.futuristic-voice-message {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.futuristic-message-current-user .futuristic-voice-message-container {
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.2),
    rgba(131, 56, 236, 0.2)
  );
  border-color: rgba(255, 255, 255, 0.2);
}

.futuristic-message-other-user .futuristic-voice-message-container {
  background: rgba(30, 30, 40, 0.4);
  border-color: rgba(0, 247, 255, 0.15);
}

.futuristic-voice-play-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 2;
  animation: pulse-slow 4s infinite;
}

.futuristic-voice-play-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(0, 247, 255, 0.2) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.futuristic-voice-play-button:hover::before {
  opacity: 1;
}

.futuristic-message-current-user .futuristic-voice-play-button {
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--accent-color);
  border: 2px solid rgba(0, 247, 255, 0.5);
}

.futuristic-message-other-user .futuristic-voice-play-button {
  background-color: rgba(0, 247, 255, 0.9);
  color: rgba(10, 10, 20, 0.9);
  border: 2px solid rgba(0, 247, 255, 0.2);
}

.futuristic-voice-play-button:hover {
  transform: scale(1.2) translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.5);
  animation: none;
}

.futuristic-voice-play-button i {
  font-size: 16px;
  transition: all 0.3s ease;
}

.futuristic-voice-play-button:hover i {
  transform: scale(1.1);
}

.futuristic-voice-waveform {
  display: flex;
  align-items: center;
  gap: 3px;
  height: 36px;
  padding: 0 5px;
  position: relative;
}

.futuristic-voice-waveform::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 247, 255, 0.1),
    transparent
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  animation: waveformPulse 2s infinite;
  border-radius: 8px;
  pointer-events: none;
}

@keyframes waveformPulse {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.futuristic-voice-bar {
  width: 3px;
  background-color: currentColor;
  border-radius: 4px;
  transition: height 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 0 5px rgba(0, 247, 255, 0.3);
}

.futuristic-message-current-user .futuristic-voice-bar {
  background-color: rgba(255, 255, 255, 0.8);
}

.futuristic-message-other-user .futuristic-voice-bar {
  background-color: rgba(0, 247, 255, 0.8);
}

.futuristic-voice-waveform:hover .futuristic-voice-bar {
  transform: scaleY(1.1);
  box-shadow: 0 0 8px rgba(0, 247, 255, 0.5);
}

/* Supprimer les contours bleus des éléments audio */
audio:focus,
audio:active,
audio:hover,
.voice-message-player:focus,
.voice-message-player:active,
.voice-message-player:hover,
app-voice-message-player {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

/* Styles pour les messages vocaux futuristes */
.futuristic-message-current-user app-voice-message-player .waveform-bar {
  background-color: rgba(255, 255, 255, 0.7) !important;
}

.futuristic-message-current-user app-voice-message-player .waveform-bar.active {
  background-color: #ffffff !important;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.8) !important;
}

.futuristic-message-other-user app-voice-message-player .waveform-bar {
  background-color: rgba(0, 247, 255, 0.5) !important;
}

.futuristic-message-other-user app-voice-message-player .waveform-bar.active {
  background-color: var(--accent-color) !important;
  box-shadow: 0 0 5px var(--accent-color) !important;
}

/* Styles pour le bouton de lecture dans les messages vocaux */
.futuristic-message-current-user app-voice-message-player .play-button {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: var(--accent-color) !important;
  box-shadow: var(--glow-effect) !important;
}

.futuristic-message-other-user app-voice-message-player .play-button {
  background-color: var(--accent-color) !important;
  color: var(--dark-bg) !important;
  box-shadow: var(--glow-effect) !important;
}

/* Style pour l'image dans le modal */
.image-modal img {
  animation: scaleIn 0.3s ease-in-out;
  transition: transform 0.2s ease;
}

/* Indicateur de frappe futuriste */
.futuristic-typing-indicator {
  display: flex;
  align-items: flex-end;
  margin: 10px 0;
  animation: fadeIn 0.3s ease-out;
}

.futuristic-typing-bubble {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 10px 15px;
  margin-left: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.futuristic-typing-dots {
  display: flex;
  gap: 4px;
}

.futuristic-typing-dot {
  width: 8px;
  height: 8px;
  background-color: var(--accent-color);
  border-radius: 50%;
  animation: typingPulse 1.5s infinite ease-in-out;
  box-shadow: 0 0 5px var(--accent-color);
}

@keyframes typingPulse {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1;
    box-shadow: 0 0 8px var(--accent-color);
  }
}

/* État sans messages futuriste */
.futuristic-no-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.futuristic-no-messages-icon {
  font-size: 3rem;
  color: var(--accent-color);
  margin-bottom: 20px;
  opacity: 0.7;
}

.futuristic-no-messages-text {
  color: var(--text-dim);
  font-size: 1rem;
  margin-bottom: 30px;
  line-height: 1.5;
}

/* Aperçu de fichier futuriste */
.futuristic-file-preview {
  position: relative;
  margin-bottom: 10px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  max-width: 200px;
  border: 2px solid rgba(0, 247, 255, 0.3);
  box-shadow: var(--glow-effect);
}

.futuristic-preview-image {
  width: 100%;
  display: block;
}

.futuristic-remove-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.futuristic-remove-button:hover {
  background: rgba(255, 0, 0, 0.7);
  transform: scale(1.1);
}

/* ========================================
   STYLES POUR L'INTERFACE D'APPEL ACTIF
   ======================================== */

/* Modal d'appel actif */
.active-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 9998;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease-out;
}

.active-call-modal.minimized {
  width: 320px;
  height: 180px;
  top: 20px;
  right: 20px;
  left: auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(0, 247, 255, 0.3);
}

/* Interface d'appel vidéo */
.video-call-interface {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  overflow: hidden;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #1a1a1a;
}

.local-video {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  border-radius: 12px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: #2a2a2a;
  z-index: 10;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
}

.local-video:hover {
  border-color: rgba(0, 247, 255, 0.6);
  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.3);
}

.local-avatar {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
  z-index: 10;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.local-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

/* Interface d'appel audio */
.audio-call-interface {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.audio-call-interface::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 247, 255, 0.1) 0%,
    transparent 70%
  );
  animation: ambientGlow 4s infinite alternate;
}

.audio-call-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.call-participant-avatar {
  width: 200px;
  height: 200px;
  margin: 0 auto 2rem;
  border-radius: 50%;
  overflow: hidden;
  border: 6px solid rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.4);
}

.call-participant-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.call-participant-name {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.call-status {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
  color: white;
  font-weight: 300;
}

.call-quality-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 1rem;
}

.quality-text {
  text-transform: capitalize;
  font-weight: 500;
}

/* Contrôles d'appel */
.call-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem;
  transition: opacity 0.3s ease;
  z-index: 20;
}

.call-controls.hidden {
  opacity: 0;
  pointer-events: none;
}

.call-info {
  text-align: center;
  margin-bottom: 1rem;
  color: white;
}

.call-duration {
  font-size: 1.1rem;
  font-weight: 600;
  margin-right: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.call-participant {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 300;
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  align-items: center;
}

.control-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.control-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.control-btn:hover::before {
  opacity: 1;
}

.control-btn.active {
  background: rgba(255, 107, 107, 0.8);
  border-color: rgba(255, 107, 107, 0.6);
}

.control-btn.active:hover {
  background: rgba(255, 107, 107, 0.9);
}

.control-btn.end-call {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-color: rgba(255, 107, 107, 0.6);
}

.control-btn.end-call:hover {
  background: linear-gradient(135deg, #ff5252, #d32f2f);
  transform: scale(1.15);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

/* Interface minimisée */
.minimized-call-interface {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.minimized-call-interface::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(0, 247, 255, 0.1) 0%,
    transparent 50%,
    rgba(131, 56, 236, 0.1) 100%
  );
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.minimized-info {
  display: flex;
  flex-direction: column;
  z-index: 2;
}

.minimized-duration {
  font-size: 1.1rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.minimized-participant {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 300;
}

.minimized-controls {
  display: flex;
  gap: 0.5rem;
  z-index: 2;
}

.minimized-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.minimized-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.minimized-btn.end-call {
  background: rgba(255, 107, 107, 0.8);
  border-color: rgba(255, 107, 107, 0.6);
}

.minimized-btn.end-call:hover {
  background: rgba(255, 107, 107, 0.9);
  transform: scale(1.15);
}

/* Responsive pour les appels */
@media (max-width: 768px) {
  .local-video {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }

  .local-avatar {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }

  .local-avatar img {
    width: 40px;
    height: 40px;
  }

  .call-participant-avatar {
    width: 150px;
    height: 150px;
  }

  .call-participant-name {
    font-size: 1.5rem;
  }

  .control-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .active-call-modal.minimized {
    width: 280px;
    height: 160px;
  }

  .control-buttons {
    gap: 1rem;
  }

  .call-controls {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .call-participant-avatar {
    width: 120px;
    height: 120px;
  }

  .call-participant-name {
    font-size: 1.3rem;
  }

  .call-status {
    font-size: 1rem;
  }

  .control-btn {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .active-call-modal.minimized {
    width: 250px;
    height: 140px;
  }
}

/* ========================================
   STYLES POUR LE PANNEAU DE NOTIFICATIONS
   ======================================== */

/* Overlay du panneau */
.notification-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

/* Panneau principal */
.notification-panel {
  width: 90%;
  max-width: 600px;
  height: 80%;
  max-height: 700px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(0, 247, 255, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* En-tête du panneau */
.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 2px solid rgba(0, 247, 255, 0.3);
}

.notification-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.notification-title i {
  font-size: 1.5rem;
  color: #00f7ff;
}

.notification-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
  animation: pulse 2s infinite;
}

.notification-actions {
  display: flex;
  gap: 0.5rem;
}

.notification-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.notification-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.notification-btn.close-btn:hover {
  background: rgba(255, 107, 107, 0.8);
}

/* Filtres et options */
.notification-filters {
  padding: 1rem 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.filter-tab {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: #a0a0a0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-tab:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.filter-tab.active {
  background: linear-gradient(135deg, #00f7ff, #0099cc);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.3);
}

/* Actions de sélection */
.selection-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.selection-controls {
  display: flex;
  align-items: center;
}

.select-all-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #a0a0a0;
  font-size: 0.9rem;
}

.select-all-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.select-all-checkbox input[type="checkbox"]:checked + .checkmark {
  background: linear-gradient(135deg, #00f7ff, #0099cc);
  border-color: #00f7ff;
}

.select-all-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.bulk-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mark-read-btn {
  background: linear-gradient(135deg, #51cf66, #40c057);
  color: white;
}

.mark-read-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #40c057, #37b24d);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
}

.delete-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.delete-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff5252, #d32f2f);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.mark-all-read-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.mark-all-read-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Actions globales */
.global-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.delete-all-btn {
  background: linear-gradient(135deg, #ff6b69, #ff5252);
  color: white;
}

.delete-all-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff5252, #f44336);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 107, 105, 0.3);
}

.delete-all-btn:disabled {
  background: linear-gradient(135deg, #ccc, #999);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Liste des notifications */
.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.1);
}

.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00f7ff, #0099cc);
  border-radius: 3px;
}

/* États de chargement et vide */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #a0a0a0;
  text-align: center;
}

.loading-state i,
.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #00f7ff;
}

.loading-state span,
.empty-state span {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Éléments de notification */
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 247, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.notification-item.unread {
  background: rgba(0, 247, 255, 0.1);
  border-color: rgba(0, 247, 255, 0.3);
}

.notification-item.selected {
  background: rgba(0, 247, 255, 0.2);
  border-color: #00f7ff;
  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);
}

.notification-checkbox {
  display: flex;
  align-items: center;
  margin-top: 0.25rem;
}

.notification-checkbox input[type="checkbox"] {
  display: none;
}

.notification-checkbox .checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.notification-checkbox input[type="checkbox"]:checked + .checkmark {
  background: linear-gradient(135deg, #00f7ff, #0099cc);
  border-color: #00f7ff;
}

.notification-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.notification-body {
  flex: 1;
  min-width: 0;
}

.notification-text {
  margin-bottom: 0.5rem;
}

.notification-sender {
  font-weight: 600;
  color: #00f7ff;
  margin-right: 0.5rem;
}

.notification-message {
  color: #e0e0e0;
  line-height: 1.4;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #a0a0a0;
}

.read-indicator {
  color: #51cf66;
}

.notification-item-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-item:hover .notification-item-actions {
  opacity: 1;
}

.item-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: #a0a0a0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.item-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.1);
}

.item-action-btn.delete:hover {
  background: rgba(255, 107, 107, 0.8);
  color: white;
}

/* Bouton charger plus */
.load-more-container {
  display: flex;
  justify-content: center;
  padding: 1rem;
}

.load-more-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.load-more-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.load-more-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Paramètres de notification */
.notification-settings {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-bottom: 2px solid rgba(0, 247, 255, 0.3);
}

.settings-header h4 {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.close-settings-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-settings-btn:hover {
  background: rgba(255, 107, 107, 0.8);
  transform: scale(1.1);
}

.settings-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.setting-item {
  margin-bottom: 1.5rem;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  color: #e0e0e0;
  font-size: 1rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.setting-label:hover {
  color: #00f7ff;
}

.setting-label input[type="checkbox"] {
  display: none;
}

.setting-checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.setting-label input[type="checkbox"]:checked + .setting-checkmark {
  background: linear-gradient(135deg, #00f7ff, #0099cc);
  border-color: #00f7ff;
}

.setting-label input[type="checkbox"]:checked + .setting-checkmark::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* Modal de confirmation de suppression */
.delete-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.delete-confirm-content {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 15px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  border: 2px solid rgba(255, 107, 107, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.3s ease-out;
}

.delete-confirm-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  color: #ff6b6b;
}

.delete-confirm-header i {
  font-size: 2rem;
}

.delete-confirm-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.delete-confirm-body {
  margin-bottom: 2rem;
  color: #e0e0e0;
  line-height: 1.5;
}

.warning-text {
  color: #ff6b6b;
  font-weight: 500;
  margin-top: 0.5rem;
}

.delete-confirm-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: transparent;
  color: #a0a0a0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.cancel-btn:hover {
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.confirm-delete-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.confirm-delete-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff5252, #d32f2f);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.confirm-delete-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive pour les notifications */
@media (max-width: 768px) {
  .notification-panel {
    width: 95%;
    height: 90%;
    margin: 1rem;
  }

  .notification-header {
    padding: 1rem;
  }

  .notification-title {
    font-size: 1.1rem;
  }

  .notification-filters {
    padding: 0.75rem 1rem;
  }

  .filter-tabs {
    flex-wrap: wrap;
  }

  .selection-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .bulk-actions {
    justify-content: center;
  }

  .notification-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .notification-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .notification-item-actions {
    opacity: 1;
  }

  .delete-confirm-content {
    padding: 1.5rem;
  }

  .delete-confirm-actions {
    flex-direction: column;
  }
}

/* ========================================
   STYLES POUR LE STATUT UTILISATEUR
   ======================================== */

/* Overlay du panneau de statut */
.user-status-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

/* Panneau principal de statut */
.user-status-panel {
  width: 90%;
  max-width: 700px;
  height: 80%;
  max-height: 800px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(0, 247, 255, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
}

/* En-tête du panneau de statut */
.status-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 2px solid rgba(0, 247, 255, 0.3);
}

.status-panel-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.status-panel-title i {
  font-size: 1.5rem;
  color: #00f7ff;
}

.online-count-badge {
  background: linear-gradient(135deg, #51cf66, #40c057);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  animation: pulse 2s infinite;
}

.status-panel-actions {
  display: flex;
  gap: 0.5rem;
}

.status-panel-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.status-panel-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.status-panel-btn.close-btn:hover {
  background: rgba(255, 107, 107, 0.8);
}

/* Filtres de statut */
.status-filters {
  padding: 1rem 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.status-filter-tab {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: #a0a0a0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-filter-tab:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.status-filter-tab.active {
  background: linear-gradient(135deg, #00f7ff, #0099cc);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.3);
}

.status-filter-tab i {
  font-size: 0.8rem;
}

/* Liste des utilisateurs */
.status-user-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.1);
}

.status-user-list::-webkit-scrollbar {
  width: 6px;
}

.status-user-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.status-user-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00f7ff, #0099cc);
  border-radius: 3px;
}

/* État vide */
.empty-status-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #a0a0a0;
  text-align: center;
}

.empty-status-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #00f7ff;
}

.empty-status-state span {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Éléments utilisateur */
.status-user-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.status-user-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 247, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Avatar utilisateur avec statut */
.status-user-avatar {
  position: relative;
  flex-shrink: 0;
}

.user-avatar-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
}

/* Informations utilisateur */
.status-user-info {
  flex: 1;
  min-width: 0;
}

.status-user-name {
  font-size: 1rem;
  font-weight: 600;
  color: #e0e0e0;
  margin-bottom: 0.25rem;
}

.status-user-details {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #a0a0a0;
}

.status-text {
  font-weight: 500;
}

.status-separator {
  color: #666;
}

.last-seen {
  color: #888;
}

/* Actions utilisateur */
.status-user-actions {
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.status-user-item:hover .status-user-actions {
  opacity: 1;
}

.status-action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: #a0a0a0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.status-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.1);
}

.status-action-btn.message-btn:hover {
  background: rgba(0, 247, 255, 0.8);
  color: white;
}

.status-action-btn.call-btn:hover {
  background: rgba(81, 207, 102, 0.8);
  color: white;
}

/* Historique de statut */
.status-history {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.status-history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-bottom: 2px solid rgba(0, 247, 255, 0.3);
}

.status-history-header h4 {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.close-history-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-history-btn:hover {
  background: rgba(255, 107, 107, 0.8);
  transform: scale(1.1);
}

.status-history-list {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.status-history-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid rgba(0, 247, 255, 0.5);
}

.history-time {
  font-size: 0.8rem;
  color: #888;
  min-width: 80px;
}

.history-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.history-user {
  font-weight: 500;
  color: #e0e0e0;
}

.history-action {
  font-weight: 600;
}

/* Responsive pour le statut utilisateur */
@media (max-width: 768px) {
  .user-status-panel {
    width: 95%;
    height: 90%;
    margin: 1rem;
  }

  .status-panel-header {
    padding: 1rem;
  }

  .status-panel-title {
    font-size: 1.1rem;
  }

  .status-filters {
    padding: 0.75rem 1rem;
  }

  .filter-tabs {
    justify-content: center;
  }

  .status-filter-tab {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .status-user-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .user-avatar-img {
    width: 40px;
    height: 40px;
  }

  .status-indicator {
    width: 14px;
    height: 14px;
    font-size: 0.6rem;
  }

  .status-user-name {
    font-size: 0.9rem;
  }

  .status-user-details {
    font-size: 0.8rem;
  }

  .status-user-actions {
    opacity: 1;
  }

  .status-action-btn {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
}

/* ========================================
   STYLES POUR LES MESSAGES VOCAUX MODERNES
   ======================================== */

/* Container principal du message vocal moderne */
.voice-message-modern {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 18px;
  max-width: 280px;
  min-width: 200px;
  position: relative;
  transition: all 0.2s ease;
  margin: 2px 0;
}

/* Messages vocaux envoyés (à droite) */
.voice-message-sent {
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
  color: white;
  margin-left: auto;
  box-shadow: var(--shadow-modern);
  border: 1px solid var(--glass-border);
}

/* Messages vocaux reçus (à gauche) */
.voice-message-received {
  background: var(--modern-white);
  background-image: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(245, 87, 108, 0.05) 100%
  );
  color: var(--modern-gray-800);
  margin-right: auto;
  box-shadow: var(--shadow-modern);
  border: 1px solid var(--modern-gray-200);
}

:host-context(.dark) .voice-message-received {
  background: var(--dark-surface);
  background-image: linear-gradient(
    135deg,
    rgba(79, 172, 254, 0.1) 0%,
    rgba(245, 87, 108, 0.1) 100%
  );
  color: var(--dark-text);
  box-shadow: var(--shadow-modern);
  border: 1px solid var(--dark-accent);
}

/* États spéciaux */
.voice-message-pending {
  opacity: 0.7;
}

.voice-message-error {
  background: #ffebee !important;
  border: 1px solid #f44336;
}

/* Bouton play moderne compact */
.voice-play-btn-modern {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.voice-message-sent .voice-play-btn-modern {
  background: var(--glass-effect);
  backdrop-filter: var(--blur-effect);
  border: 1px solid var(--glass-border);
  color: white;
}

.voice-message-received .voice-play-btn-modern {
  background: var(--accent-gradient);
  background-size: 200% 200%;
  animation: gradientShift 6s ease infinite;
  color: white;
  border: 1px solid var(--modern-gray-300);
}

:host-context(.dark) .voice-message-received .voice-play-btn-modern {
  background: var(--primary-gradient);
  background-size: 200% 200%;
  border: 1px solid var(--dark-accent);
}

.voice-play-btn-modern:hover {
  transform: scale(1.05);
}

.voice-play-btn-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.2s ease;
}

.voice-play-btn-modern:hover::before {
  opacity: 1;
}

.voice-play-btn-modern i {
  font-size: 12px;
  margin-left: 1px; /* Centrer visuellement l'icône play */
  z-index: 1;
  position: relative;
}

/* Forme d'onde moderne compacte */
.voice-waveform-modern {
  display: flex;
  align-items: center;
  gap: 2px;
  flex: 1;
  height: 20px;
  overflow: hidden;
  padding: 0 4px;
}

.voice-bar-modern {
  width: 3px;
  background: currentColor;
  border-radius: 1.5px;
  opacity: 0.6;
  transition: all 0.2s ease;
  min-height: 4px;
}

.voice-message-sent .voice-bar-modern {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

.voice-message-received .voice-bar-modern {
  background: var(--modern-gray-600);
  box-shadow: 0 0 4px rgba(102, 126, 234, 0.3);
}

:host-context(.dark) .voice-message-received .voice-bar-modern {
  background: var(--dark-text);
  box-shadow: 0 0 4px rgba(79, 172, 254, 0.4);
}

/* Animation des barres au survol */
.voice-message-modern:hover .voice-bar-modern {
  opacity: 1;
  animation: waveAnimation 1.5s ease-in-out infinite;
}

@keyframes waveAnimation {
  0%,
  100% {
    transform: scaleY(1);
    opacity: 0.6;
  }
  50% {
    transform: scaleY(1.5);
    opacity: 1;
  }
}

.voice-bar-modern:nth-child(2n) {
  animation-delay: 0.1s;
}

.voice-bar-modern:nth-child(3n) {
  animation-delay: 0.2s;
}

.voice-bar-modern:nth-child(4n) {
  animation-delay: 0.3s;
}

/* Durée du message vocal */
.voice-duration-modern {
  font-size: 11px;
  font-weight: 500;
  opacity: 0.8;
  flex-shrink: 0;
  min-width: 30px;
  text-align: right;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.voice-message-sent .voice-duration-modern {
  color: rgba(255, 255, 255, 0.9);
}

.voice-message-received .voice-duration-modern {
  color: #65676b;
}

:host-context(.dark) .voice-message-received .voice-duration-modern {
  color: #b0b3b8;
}

/* Effet de survol pour le message vocal complet */
.voice-message-modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.voice-message-sent:hover {
  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.4);
}

.voice-message-received:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

:host-context(.dark) .voice-message-received:hover {
  box-shadow: 0 4px 12px rgba(109, 120, 201, 0.3);
}

/* ========================================
   STYLES POUR LES OPTIONS DE MESSAGES MODERNES
   ======================================== */

/* Bouton d'options (trois points) moderne */
.futuristic-message-bubble .absolute.-top-2.-right-2 {
  background: var(--glass-effect) !important;
  backdrop-filter: var(--blur-effect) !important;
  border: 1px solid var(--modern-gray-300) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: var(--shadow-modern) !important;
}

.futuristic-message-bubble .absolute.-top-2.-right-2:hover {
  transform: scale(1.1) translateY(-2px) !important;
  box-shadow: var(--shadow-hover) !important;
}

/* Mode sombre pour le bouton d'options */
:host-context(.dark) .futuristic-message-bubble .absolute.-top-2.-right-2 {
  background: var(--glass-effect) !important;
  border: 1px solid var(--neon-cyan) !important;
  color: var(--neon-cyan) !important;
  text-shadow: 0 0 8px var(--neon-cyan) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
}

:host-context(.dark)
  .futuristic-message-bubble
  .absolute.-top-2.-right-2:hover {
  background: var(--neon-gradient-1) !important;
  color: white !important;
  text-shadow: none !important;
  box-shadow: var(--neon-glow-cyan) !important;
  border-color: transparent !important;
}

/* Menu d'options moderne */
.futuristic-message-bubble .absolute.top-6.right-0 {
  background: var(--modern-white) !important;
  backdrop-filter: var(--blur-effect) !important;
  border: 1px solid var(--modern-gray-200) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-modern) !important;
  overflow: hidden !important;
  animation: slideInDown 0.3s ease-out !important;
}

:host-context(.dark) .futuristic-message-bubble .absolute.top-6.right-0 {
  background: var(--dark-surface) !important;
  border: 1px solid var(--dark-accent) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Boutons d'options individuels modernes */
.futuristic-message-bubble button[class*="w-full px-3 py-2"] {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  border-radius: 0 !important;
}

.futuristic-message-bubble button[class*="w-full px-3 py-2"]::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.futuristic-message-bubble button[class*="w-full px-3 py-2"]:hover::before {
  left: 100%;
}

/* Mode sombre pour les boutons d'options */
:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"] {
  color: var(--dark-text) !important;
}

:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:hover {
  background: var(--glass-effect) !important;
  color: var(--neon-cyan) !important;
  text-shadow: 0 0 8px var(--neon-cyan) !important;
}

/* Couleurs spécifiques pour chaque action */
:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:nth-child(1):hover {
  color: var(--neon-green) !important;
  text-shadow: 0 0 8px var(--neon-green) !important;
}

:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:nth-child(2):hover {
  color: var(--neon-blue) !important;
  text-shadow: 0 0 8px var(--neon-blue) !important;
}

:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:nth-child(3):hover {
  color: var(--neon-yellow) !important;
  text-shadow: 0 0 8px var(--neon-yellow) !important;
}

:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:nth-child(4):hover {
  color: var(--neon-purple) !important;
  text-shadow: 0 0 8px var(--neon-purple) !important;
}

:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:nth-child(5):hover {
  color: var(--neon-red) !important;
  text-shadow: 0 0 8px var(--neon-red) !important;
}

/* Icônes des options avec effets fluo */
:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"]
  i {
  transition: all 0.3s ease !important;
}

:host-context(.dark)
  .futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:hover
  i {
  transform: scale(1.2) !important;
  filter: drop-shadow(0 0 4px currentColor) !important;
}

/* ========================================
   STYLES POUR LES RÉACTIONS MODERNES
   ======================================== */

/* Bouton de réaction rapide moderne */
.futuristic-message .absolute.-bottom-2.left-1\/2 {
  background: var(--modern-white) !important;
  backdrop-filter: var(--blur-effect) !important;
  border: 1px solid var(--modern-gray-300) !important;
  box-shadow: var(--shadow-modern) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.futuristic-message .absolute.-bottom-2.left-1\/2:hover {
  transform: translateX(-50%) scale(1.1) translateY(-2px) !important;
  box-shadow: var(--shadow-hover) !important;
}

/* Mode sombre pour le bouton de réaction */
:host-context(.dark) .futuristic-message .absolute.-bottom-2.left-1\/2 {
  background: var(--dark-surface) !important;
  border: 1px solid var(--neon-pink) !important;
  color: var(--neon-pink) !important;
  text-shadow: 0 0 8px var(--neon-pink) !important;
  box-shadow: 0 0 15px rgba(255, 0, 255, 0.3) !important;
}

:host-context(.dark) .futuristic-message .absolute.-bottom-2.left-1\/2:hover {
  background: var(--neon-gradient-3) !important;
  color: white !important;
  text-shadow: none !important;
  box-shadow: var(--neon-glow-pink) !important;
  border-color: transparent !important;
}

/* Sélecteur de réactions moderne */
.reaction-picker {
  background: var(--modern-white) !important;
  backdrop-filter: var(--blur-effect) !important;
  border: 1px solid var(--modern-gray-200) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-modern) !important;
  animation: bounceIn 0.3s ease-out !important;
}

:host-context(.dark) .reaction-picker {
  background: var(--dark-surface) !important;
  border: 1px solid var(--dark-accent) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0.3);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

/* Boutons d'émojis dans le sélecteur */
.reaction-picker button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 8px !important;
  position: relative !important;
  overflow: hidden !important;
}

.reaction-picker button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.reaction-picker button:hover::before {
  opacity: 1;
}

.reaction-picker button:hover {
  transform: scale(1.2) translateY(-2px) !important;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2)) !important;
}

/* Mode sombre pour les boutons d'émojis */
:host-context(.dark) .reaction-picker button:hover {
  background: var(--glass-effect) !important;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2) !important;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4)) !important;
}

/* Réactions existantes modernes */
.futuristic-message
  button[class*="flex items-center space-x-1 px-2 py-1 rounded-full"] {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 16px !important;
  backdrop-filter: var(--blur-effect) !important;
  position: relative !important;
  overflow: hidden !important;
}

.futuristic-message
  button[class*="flex items-center space-x-1 px-2 py-1 rounded-full"]:hover {
  transform: scale(1.05) translateY(-1px) !important;
  box-shadow: var(--shadow-modern) !important;
}

/* Mode sombre pour les réactions existantes */
:host-context(.dark)
  .futuristic-message
  button[class*="flex items-center space-x-1 px-2 py-1 rounded-full"] {
  border: 1px solid var(--dark-accent) !important;
}

:host-context(.dark)
  .futuristic-message
  button[class*="flex items-center space-x-1 px-2 py-1 rounded-full"]:hover {
  background: var(--glass-effect) !important;
  border-color: var(--neon-cyan) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
  color: var(--neon-cyan) !important;
  text-shadow: 0 0 8px var(--neon-cyan) !important;
}

/* ========================================
   STYLES POUR L'APERÇU DE RÉPONSE
   ======================================== */

/* Aperçu du message auquel on répond */
.reply-preview {
  background: #edf1f4;
  border-left: 4px solid #4f5fad;
  padding: 12px;
  margin: 0 16px 8px;
  border-radius: 0 8px 8px 0;
  transition: all 0.2s ease;
  animation: slideInUp 0.3s ease-out;
}

:host-context(.dark) .reply-preview {
  background: #2a2a2a;
  border-left-color: #6d78c9;
}

.reply-preview:hover {
  background: #e8ecf0;
  border-left-color: #6d78c9;
}

:host-context(.dark) .reply-preview:hover {
  background: #333333;
  border-left-color: #8a94d4;
}

/* Message auquel on répond dans les messages */
.reply-to-message {
  background: #edf1f4;
  border-left: 2px solid #4f5fad;
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 0 6px 6px 0;
  font-size: 12px;
  opacity: 0.9;
}

:host-context(.dark) .reply-to-message {
  background: #3a3a3a;
  border-left-color: #6d78c9;
}

/* ========================================
   OPTIMISATIONS RESPONSIVE POUR ÉCRANS RÉDUITS
   ======================================== */

/* Optimisations pour écrans moyens (tablettes) - CONSOLIDÉ */
@media (max-width: 1024px) and (min-width: 769px) {
  .whatsapp-chat-header {
    height: 48px !important;
    padding: 4px 8px !important;
  }

  .whatsapp-avatar {
    width: 32px !important;
    height: 32px !important;
  }

  .whatsapp-action-button {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.85rem !important;
  }

  .whatsapp-actions {
    gap: 12px !important;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 160px) !important;
    padding: 0.5rem !important;
  }

  .whatsapp-input-container {
    min-height: 48px !important;
    padding: 6px 8px !important;
  }

  .voice-message-modern {
    max-width: 260px !important;
    min-width: 180px !important;
  }
}

/* Optimisations pour écrans petits (mobiles) */
@media (max-width: 768px) {
  .whatsapp-chat-header {
    height: 44px;
    padding: 4px 6px;
  }

  .whatsapp-avatar {
    width: 28px;
    height: 28px;
  }

  .whatsapp-username {
    font-size: 0.875rem;
  }

  .whatsapp-status {
    font-size: 0.7rem;
  }

  .whatsapp-action-button {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.9rem !important;
    /* Maintenir les couleurs fluorescentes sur tablettes */
  }

  .whatsapp-actions {
    gap: 8px;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 140px);
    padding: 0.4rem;
  }

  .futuristic-message-bubble {
    max-width: 85%;
    padding: 0.5rem 0.7rem;
    font-size: 0.85rem;
  }

  .whatsapp-input-container {
    min-height: 44px;
    padding: 4px 6px;
  }

  .whatsapp-input-field {
    padding: 6px 12px;
    font-size: 0.875rem;
  }

  .whatsapp-send-button,
  .whatsapp-voice-button {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .whatsapp-tool-button {
    width: 28px;
    height: 28px;
    font-size: 0.85rem;
  }

  .voice-message-modern {
    max-width: 240px;
    min-width: 160px;
    padding: 6px 10px;
  }

  .voice-play-btn-modern {
    width: 28px;
    height: 28px;
  }

  .voice-play-btn-modern i {
    font-size: 10px;
  }

  .voice-duration-modern {
    font-size: 10px;
    min-width: 25px;
  }

  .reply-preview {
    margin: 0 8px 6px;
    padding: 8px;
  }
}

/* Optimisations pour très petits écrans */
@media (max-width: 480px) {
  .whatsapp-chat-header {
    height: 40px;
    padding: 2px 4px;
  }

  .whatsapp-avatar {
    width: 24px;
    height: 24px;
  }

  .whatsapp-username {
    font-size: 0.8rem;
  }

  .whatsapp-status {
    font-size: 0.65rem;
  }

  .whatsapp-action-button {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.9rem !important;
    /* Maintenir les couleurs fluorescentes même sur petits écrans */
    background: linear-gradient(
      135deg,
      rgba(0, 247, 255, 0.1) 0%,
      rgba(255, 20, 147, 0.1) 50%,
      rgba(57, 255, 20, 0.1) 100%
    ) !important;
    border: 1px solid rgba(0, 247, 255, 0.3) !important;
    color: #00f7ff !important;
  }

  .whatsapp-actions {
    gap: 6px;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 120px);
    padding: 0.3rem;
  }

  .futuristic-message-bubble {
    max-width: 90%;
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }

  .whatsapp-input-container {
    min-height: 40px;
    padding: 2px 4px;
  }

  .whatsapp-input-field {
    padding: 4px 10px;
    font-size: 0.8rem;
  }

  .whatsapp-send-button,
  .whatsapp-voice-button {
    width: 36px !important;
    height: 36px !important;
    font-size: 1rem !important;
    /* Maintenir les couleurs fluorescentes sur petits écrans */
  }

  .whatsapp-tool-button {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }

  .voice-message-modern {
    max-width: 200px;
    min-width: 140px;
    padding: 4px 8px;
    gap: 6px;
  }

  .voice-play-btn-modern {
    width: 24px;
    height: 24px;
  }

  .voice-play-btn-modern i {
    font-size: 8px;
  }

  .voice-waveform-modern {
    height: 16px;
    gap: 1px;
  }

  .voice-bar-modern {
    width: 2px;
  }

  .voice-duration-modern {
    font-size: 9px;
    min-width: 20px;
  }

  .reply-preview {
    margin: 0 4px 4px;
    padding: 6px;
    font-size: 0.75rem;
  }
}

/* ========================================
   STYLES POUR LES ICÔNES DE STATUT MODERNES
   ======================================== */

/* Icônes de statut de lecture modernes */
.futuristic-message-status i {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
}

.futuristic-message-status i:hover {
  transform: scale(1.1) !important;
}

/* Mode sombre pour les icônes de statut */
:host-context(.dark) .futuristic-message-status i {
  color: var(--neon-green) !important;
  text-shadow: 0 0 6px var(--neon-green) !important;
  filter: drop-shadow(0 0 4px var(--neon-green)) !important;
}

:host-context(.dark) .futuristic-message-status i.fa-check-double {
  color: var(--neon-blue) !important;
  text-shadow: 0 0 6px var(--neon-blue) !important;
  filter: drop-shadow(0 0 4px var(--neon-blue)) !important;
}

/* Icônes d'épinglage modernes */
.futuristic-message-text i.fa-thumbtack {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:host-context(.dark) .futuristic-message-text i.fa-thumbtack {
  color: var(--neon-yellow) !important;
  text-shadow: 0 0 6px var(--neon-yellow) !important;
  filter: drop-shadow(0 0 4px var(--neon-yellow)) !important;
  animation: pinGlow 2s ease-in-out infinite alternate !important;
}

@keyframes pinGlow {
  0% {
    text-shadow: 0 0 6px var(--neon-yellow);
    filter: drop-shadow(0 0 4px var(--neon-yellow));
  }
  100% {
    text-shadow: 0 0 12px var(--neon-yellow);
    filter: drop-shadow(0 0 8px var(--neon-yellow));
  }
}

/* Icônes d'avatar modernes */
.futuristic-avatar img {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: 2px solid transparent !important;
}

.futuristic-avatar img:hover {
  transform: scale(1.05) !important;
}

:host-context(.dark) .futuristic-avatar img {
  border: 2px solid var(--neon-cyan) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
}

:host-context(.dark) .futuristic-avatar img:hover {
  box-shadow: 0 0 25px rgba(0, 255, 255, 0.5) !important;
  border-color: var(--neon-pink) !important;
}

/* Icônes d'erreur et d'état modernes */
.futuristic-message-error {
  border: 1px solid var(--neon-red) !important;
  box-shadow: 0 0 15px rgba(255, 51, 102, 0.3) !important;
}

:host-context(.dark) .futuristic-message-error {
  border: 1px solid var(--neon-red) !important;
  box-shadow: 0 0 20px rgba(255, 51, 102, 0.4) !important;
  animation: errorPulse 1.5s ease-in-out infinite !important;
}

@keyframes errorPulse {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(255, 51, 102, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 51, 102, 0.6);
  }
}

/* Icônes de chargement modernes */
.futuristic-message-pending {
  opacity: 0.7 !important;
}

:host-context(.dark) .futuristic-message-pending {
  border: 1px solid var(--neon-orange) !important;
  box-shadow: 0 0 15px rgba(255, 102, 0, 0.3) !important;
  animation: pendingGlow 2s ease-in-out infinite !important;
}

@keyframes pendingGlow {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(255, 102, 0, 0.3);
    opacity: 0.7;
  }
  50% {
    box-shadow: 0 0 25px rgba(255, 102, 0, 0.5);
    opacity: 0.9;
  }
}

/* Icônes d'expansion d'image modernes */
.futuristic-image-overlay i {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:host-context(.dark) .futuristic-image-overlay i {
  color: var(--neon-cyan) !important;
  text-shadow: 0 0 8px var(--neon-cyan) !important;
  filter: drop-shadow(0 0 6px var(--neon-cyan)) !important;
}

.futuristic-image-overlay:hover i {
  transform: scale(1.2) !important;
}

:host-context(.dark) .futuristic-image-overlay:hover i {
  text-shadow: 0 0 15px var(--neon-cyan) !important;
  filter: drop-shadow(0 0 10px var(--neon-cyan)) !important;
}

/* ========================================
   STYLES POUR LES BADGES DE NOTIFICATION MODERNES
   ======================================== */

/* Badge de notification moderne (style carré) */
.whatsapp-action-button .absolute.-top-1.-right-1 {
  background: var(--neon-gradient-3) !important;
  background-size: 200% 200% !important;
  animation: gradientShift 4s ease infinite !important;
  border: 1px solid var(--modern-white) !important;
  border-radius: 6px !important; /* Carré avec coins arrondis */
  width: auto !important;
  min-width: 18px !important;
  height: 18px !important;
  padding: 0 4px !important;
  font-size: 10px !important;
  font-weight: 700 !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: var(--shadow-modern) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  z-index: 10 !important;
}

/* Mode sombre pour les badges */
:host-context(.dark) .whatsapp-action-button .absolute.-top-1.-right-1 {
  background: var(--neon-gradient-1) !important;
  background-size: 200% 200% !important;
  border: 1px solid var(--neon-cyan) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.4) !important;
  animation: gradientShift 4s ease infinite, neonPulse 2s ease-in-out infinite !important;
}

@keyframes neonPulse {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(0, 255, 255, 0.6);
    transform: scale(1.05);
  }
}

/* Badge spécifique pour les notifications (rouge/orange) */
.whatsapp-action-button
  .absolute.-top-1.-right-1.bg-gradient-to-r.from-\[#ff6b69\] {
  background: var(--warning-gradient) !important;
  background-size: 200% 200% !important;
  animation: gradientShift 4s ease infinite,
    urgentPulse 1.5s ease-in-out infinite !important;
}

:host-context(.dark)
  .whatsapp-action-button
  .absolute.-top-1.-right-1.bg-gradient-to-r.from-\[#ff6b69\] {
  background: var(--neon-gradient-3) !important;
  border: 1px solid var(--neon-red) !important;
  box-shadow: 0 0 20px rgba(255, 51, 102, 0.5) !important;
}

@keyframes urgentPulse {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(255, 51, 102, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 51, 102, 0.8);
    transform: scale(1.1);
  }
}

/* Badge pour les messages épinglés (bleu/violet) */
.whatsapp-action-button .absolute.-top-1.-right-1.bg-\[#4f5fad\] {
  background: var(--primary-gradient) !important;
  background-size: 200% 200% !important;
  animation: gradientShift 4s ease infinite !important;
}

:host-context(.dark)
  .whatsapp-action-button
  .absolute.-top-1.-right-1.bg-\[#4f5fad\] {
  background: var(--neon-gradient-4) !important;
  border: 1px solid var(--neon-purple) !important;
  box-shadow: 0 0 15px rgba(153, 0, 255, 0.4) !important;
}

/* Badge pour les messages vocaux (vert/cyan) */
.whatsapp-action-button
  .absolute.-top-1.-right-1.bg-gradient-to-r.from-\[#4f5fad\].to-\[#6d78c9\] {
  background: var(--accent-gradient) !important;
  background-size: 200% 200% !important;
  animation: gradientShift 4s ease infinite !important;
}

:host-context(.dark)
  .whatsapp-action-button
  .absolute.-top-1.-right-1.bg-gradient-to-r.from-\[#4f5fad\].to-\[#6d78c9\] {
  background: var(--neon-gradient-2) !important;
  border: 1px solid var(--neon-green) !important;
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.4) !important;
}

/* Effet de survol pour tous les badges */
.whatsapp-action-button:hover .absolute.-top-1.-right-1 {
  transform: scale(1.1) translateY(-1px) !important;
  box-shadow: var(--shadow-hover) !important;
}

:host-context(.dark) .whatsapp-action-button:hover .absolute.-top-1.-right-1 {
  box-shadow: 0 0 30px currentColor !important;
}

/* Badge dans le panneau de notifications */
.notification-badge {
  background: var(--neon-gradient-3) !important;
  background-size: 200% 200% !important;
  animation: gradientShift 4s ease infinite !important;
  border: 1px solid var(--modern-white) !important;
  border-radius: 8px !important;
  padding: 4px 8px !important;
  font-size: 11px !important;
  font-weight: 700 !important;
  min-width: 24px !important;
  text-align: center !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: var(--shadow-modern) !important;
}

:host-context(.dark) .notification-badge {
  background: var(--neon-gradient-1) !important;
  border: 1px solid var(--neon-cyan) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5) !important;
  animation: gradientShift 4s ease infinite, neonPulse 2s ease-in-out infinite !important;
}

/* Badge de compteur en ligne */
.online-count-badge {
  background: var(--success-gradient) !important;
  background-size: 200% 200% !important;
  animation: gradientShift 4s ease infinite !important;
  border: 1px solid var(--modern-white) !important;
  border-radius: 8px !important;
  padding: 4px 12px !important;
  font-size: 11px !important;
  font-weight: 700 !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: var(--shadow-modern) !important;
}

:host-context(.dark) .online-count-badge {
  background: var(--neon-gradient-2) !important;
  border: 1px solid var(--neon-green) !important;
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.5) !important;
}

/* ========================================
   STYLES POUR LES ICÔNES DE NOTIFICATION MODERNES
   ======================================== */

/* Icônes de notification dans le panneau */
.notification-icon {
  width: 44px !important;
  height: 44px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.2rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  border: 1px solid transparent !important;
}

/* Effet de lueur pour les icônes */
.notification-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-icon:hover::before {
  opacity: 1;
}

/* Mode sombre pour les icônes de notification */
:host-context(.dark) .notification-icon {
  background: var(--glass-effect) !important;
  backdrop-filter: var(--blur-effect) !important;
  border: 1px solid var(--dark-accent) !important;
  box-shadow: var(--shadow-modern) !important;
}

:host-context(.dark) .notification-icon:hover {
  transform: scale(1.05) translateY(-2px) !important;
  box-shadow: var(--shadow-hover) !important;
}

/* Couleurs spécifiques pour chaque type de notification en mode sombre */
:host-context(.dark) .notification-icon.text-blue-500 {
  background: var(--neon-gradient-1) !important;
  color: white !important;
  border: 1px solid var(--neon-blue) !important;
  box-shadow: 0 0 15px rgba(0, 153, 255, 0.4) !important;
}

:host-context(.dark) .notification-icon.text-green-500 {
  background: var(--neon-gradient-2) !important;
  color: white !important;
  border: 1px solid var(--neon-green) !important;
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.4) !important;
}

:host-context(.dark) .notification-icon.text-purple-500 {
  background: var(--neon-gradient-4) !important;
  color: white !important;
  border: 1px solid var(--neon-purple) !important;
  box-shadow: 0 0 15px rgba(153, 0, 255, 0.4) !important;
}

:host-context(.dark) .notification-icon.text-red-500 {
  background: var(--neon-gradient-3) !important;
  color: white !important;
  border: 1px solid var(--neon-red) !important;
  box-shadow: 0 0 15px rgba(255, 51, 102, 0.4) !important;
}

:host-context(.dark) .notification-icon.text-yellow-500 {
  background: linear-gradient(
    135deg,
    var(--neon-yellow) 0%,
    var(--neon-orange) 100%
  ) !important;
  color: black !important;
  border: 1px solid var(--neon-yellow) !important;
  box-shadow: 0 0 15px rgba(255, 255, 0, 0.4) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

:host-context(.dark) .notification-icon.text-gray-500 {
  background: var(--glass-effect) !important;
  color: var(--neon-cyan) !important;
  border: 1px solid var(--neon-cyan) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
}

:host-context(.dark) .notification-icon.text-cyan-500 {
  background: var(--neon-gradient-1) !important;
  color: white !important;
  border: 1px solid var(--neon-cyan) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.4) !important;
}

/* Animation des icônes au survol */
:host-context(.dark) .notification-icon i {
  transition: all 0.3s ease !important;
  filter: drop-shadow(0 0 4px currentColor) !important;
}

:host-context(.dark) .notification-icon:hover i {
  transform: scale(1.1) !important;
  filter: drop-shadow(0 0 8px currentColor) !important;
}

/* Boutons d'action dans le panneau de notifications */
.notification-btn {
  background: var(--glass-effect) !important;
  backdrop-filter: var(--blur-effect) !important;
  border: 1px solid var(--modern-gray-300) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:host-context(.dark) .notification-btn {
  border: 1px solid var(--neon-cyan) !important;
  color: var(--neon-cyan) !important;
  text-shadow: 0 0 6px var(--neon-cyan) !important;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.2) !important;
}

:host-context(.dark) .notification-btn:hover {
  background: var(--neon-gradient-1) !important;
  color: white !important;
  text-shadow: none !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5) !important;
  transform: scale(1.05) translateY(-2px) !important;
}

:host-context(.dark) .notification-btn.close-btn:hover {
  background: var(--neon-gradient-3) !important;
  border: 1px solid var(--neon-red) !important;
  box-shadow: 0 0 20px rgba(255, 51, 102, 0.5) !important;
}

/* Icônes dans l'en-tête du panneau */
.notification-title i {
  color: var(--neon-cyan) !important;
  text-shadow: 0 0 8px var(--neon-cyan) !important;
  filter: drop-shadow(0 0 6px var(--neon-cyan)) !important;
  animation: iconGlow 3s ease-in-out infinite alternate !important;
}

@keyframes iconGlow {
  0% {
    text-shadow: 0 0 8px var(--neon-cyan);
    filter: drop-shadow(0 0 6px var(--neon-cyan));
  }
  100% {
    text-shadow: 0 0 15px var(--neon-cyan);
    filter: drop-shadow(0 0 12px var(--neon-cyan));
  }
}

/* ========================================
   OPTIMISATIONS POUR ZOOM ET AFFICHAGE
   ======================================== */

/* Variables CSS pour adaptation au zoom - CONSOLIDÉES */
:root {
  --zoom-scale: 1;
  --header-height: 52px;
  --input-height: 52px;
  --button-size: 40px;
  --tool-button-size: 32px;
  --avatar-size: 40px;
  --font-base: 1rem;
  --font-small: 0.875rem;
  --font-tiny: 0.75rem;
  --spacing-base: 0.5rem;
  --spacing-small: 0.25rem;
}

/* Adaptation automatique pour zoom élevé */
@media (min-resolution: 1.25dppx) {
  :root {
    --zoom-scale: 0.8;
    --header-height: 42px;
    --input-height: 42px;
    --button-size: 32px;
    --tool-button-size: 26px;
    --avatar-size: 32px;
    --font-base: 0.85rem;
    --font-small: 0.75rem;
    --font-tiny: 0.65rem;
    --spacing-base: 0.4rem;
    --spacing-small: 0.2rem;
  }
}

@media (min-resolution: 1.5dppx) {
  :root {
    --zoom-scale: 0.7;
    --header-height: 36px;
    --input-height: 36px;
    --button-size: 28px;
    --tool-button-size: 22px;
    --avatar-size: 28px;
    --font-base: 0.75rem;
    --font-small: 0.65rem;
    --font-tiny: 0.55rem;
    --spacing-base: 0.3rem;
    --spacing-small: 0.15rem;
  }
}

@media (min-resolution: 2dppx) {
  :root {
    --zoom-scale: 0.6;
    --header-height: 30px;
    --input-height: 30px;
    --button-size: 24px;
    --tool-button-size: 18px;
    --avatar-size: 24px;
    --font-base: 0.65rem;
    --font-small: 0.55rem;
    --font-tiny: 0.45rem;
    --spacing-base: 0.2rem;
    --spacing-small: 0.1rem;
  }
}

/* Application des variables aux éléments avec fallbacks */
.whatsapp-chat-header {
  height: var(--header-height, 52px) !important;
  padding: var(--spacing-small, 0.25rem) var(--spacing-base, 0.5rem) !important;
  min-height: 40px !important;
  max-height: 60px !important;
}

.whatsapp-input-container {
  min-height: var(--input-height, 52px) !important;
  padding: var(--spacing-small, 0.25rem) var(--spacing-base, 0.5rem) !important;
  max-height: 80px !important;
}

/* STYLES PRIORITAIRES POUR LES BOUTONS D'ACTION AVEC COULEURS FLUORESCENTES */
.whatsapp-action-button {
  width: var(--button-size, 42px) !important;
  height: var(--button-size, 42px) !important;
  font-size: var(--font-small, 1.1rem) !important;
  min-width: 36px !important;
  min-height: 36px !important;
  /* Forcer la visibilité de tous les boutons */
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  /* Forcer l'affichage des couleurs fluorescentes */
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.1) 0%,
    rgba(255, 20, 147, 0.1) 50%,
    rgba(57, 255, 20, 0.1) 100%
  ) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
  color: #00f7ff !important;
  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  /* Layout garanti */
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  position: relative !important;
  z-index: 5 !important;
}

/* STYLES PRIORITAIRES POUR ICÔNES SPÉCIFIQUES AVEC COULEURS FLUORESCENTES */

/* ========================================
   STYLES SPÉCIFIQUES PAR CLASSE - COULEURS FLUORESCENTES GARANTIES
   ======================================== */

/* ========================================
   NOUVELLES COULEURS MODERNES POUR ICÔNES - ULTRA FLUORESCENTES
   ======================================== */

/* Bouton appel audio - Vert laser holographique PRIORITAIRE */
.btn-audio-call {
  border-color: rgba(0, 255, 127, 0.7) !important;
  color: #00ff7f !important;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 127, 0.25) 0%,
    rgba(57, 255, 20, 0.25) 50%,
    rgba(0, 255, 0, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(0, 255, 127, 0.6),
    inset 0 0 20px rgba(0, 255, 127, 0.2), 0 0 50px rgba(57, 255, 20, 0.3) !important;
  animation: pulseGreenLaser 2s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-audio-call:hover {
  box-shadow: 0 0 50px rgba(0, 255, 127, 1), 0 0 100px rgba(57, 255, 20, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(0, 255, 127, 1) !important;
  border-color: rgba(0, 255, 127, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(2deg) !important;
  animation: pulseGreenLaser 0.8s ease-in-out infinite !important;
}

/* Bouton appel vidéo - Plasma magenta-cyan PRIORITAIRE */
.btn-video-call {
  border-color: rgba(255, 0, 255, 0.7) !important;
  color: #ff00ff !important;
  background: linear-gradient(
    45deg,
    rgba(255, 0, 255, 0.25) 0%,
    rgba(0, 255, 255, 0.25) 50%,
    rgba(138, 43, 226, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(255, 0, 255, 0.6),
    inset 0 0 20px rgba(255, 0, 255, 0.2), 0 0 50px rgba(0, 255, 255, 0.3) !important;
  animation: pulseMagentaCyan 2.5s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-video-call:hover {
  box-shadow: 0 0 50px rgba(255, 0, 255, 1), 0 0 100px rgba(0, 255, 255, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(255, 0, 255, 1) !important;
  border-color: rgba(255, 0, 255, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(-2deg) !important;
  animation: pulseMagentaCyan 0.8s ease-in-out infinite !important;
}

/* Bouton recherche - Or électrique PRIORITAIRE */
.btn-search {
  border-color: rgba(255, 215, 0, 0.7) !important;
  color: #ffd700 !important;
  background: linear-gradient(
    135deg,
    rgba(255, 215, 0, 0.25) 0%,
    rgba(255, 255, 0, 0.25) 50%,
    rgba(255, 140, 0, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(255, 215, 0, 0.6),
    inset 0 0 20px rgba(255, 215, 0, 0.2), 0 0 50px rgba(255, 255, 0, 0.3) !important;
  animation: pulseGold 3s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-search:hover {
  box-shadow: 0 0 50px rgba(255, 215, 0, 1), 0 0 100px rgba(255, 255, 0, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(255, 215, 0, 1) !important;
  border-color: rgba(255, 215, 0, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(3deg) !important;
  animation: pulseGold 0.8s ease-in-out infinite !important;
}

/* Bouton notifications - Plasma rouge-orange PRIORITAIRE */
.btn-notifications {
  border-color: rgba(255, 69, 0, 0.7) !important;
  color: #ff4500 !important;
  background: linear-gradient(
    135deg,
    rgba(255, 69, 0, 0.25) 0%,
    rgba(255, 0, 0, 0.25) 50%,
    rgba(255, 20, 147, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(255, 69, 0, 0.6),
    inset 0 0 20px rgba(255, 69, 0, 0.2), 0 0 50px rgba(255, 0, 0, 0.3) !important;
  animation: pulseRedOrange 2s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-notifications:hover {
  box-shadow: 0 0 50px rgba(255, 69, 0, 1), 0 0 100px rgba(255, 0, 0, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(255, 69, 0, 1) !important;
  border-color: rgba(255, 69, 0, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(-3deg) !important;
  animation: pulseRedOrange 0.8s ease-in-out infinite !important;
}

/* Bouton épinglé - Violet holographique PRIORITAIRE */
.btn-pinned {
  border-color: rgba(138, 43, 226, 0.7) !important;
  color: #8a2be2 !important;
  background: linear-gradient(
    135deg,
    rgba(138, 43, 226, 0.25) 0%,
    rgba(75, 0, 130, 0.25) 50%,
    rgba(148, 0, 211, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(138, 43, 226, 0.6),
    inset 0 0 20px rgba(138, 43, 226, 0.2), 0 0 50px rgba(75, 0, 130, 0.3) !important;
  animation: pulseViolet 2.5s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-pinned:hover {
  box-shadow: 0 0 50px rgba(138, 43, 226, 1), 0 0 100px rgba(75, 0, 130, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(138, 43, 226, 1) !important;
  border-color: rgba(138, 43, 226, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(2deg) !important;
  animation: pulseViolet 0.8s ease-in-out infinite !important;
}

/* Bouton statut - Émeraude électrique PRIORITAIRE */
.btn-status {
  border-color: rgba(0, 255, 146, 0.7) !important;
  color: #00ff92 !important;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 146, 0.25) 0%,
    rgba(0, 255, 127, 0.25) 50%,
    rgba(32, 178, 170, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(0, 255, 146, 0.6),
    inset 0 0 20px rgba(0, 255, 146, 0.2), 0 0 50px rgba(0, 255, 127, 0.3) !important;
  animation: pulseEmerald 2.2s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-status:hover {
  box-shadow: 0 0 50px rgba(0, 255, 146, 1), 0 0 100px rgba(0, 255, 127, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(0, 255, 146, 1) !important;
  border-color: rgba(0, 255, 146, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(-2deg) !important;
  animation: pulseEmerald 0.8s ease-in-out infinite !important;
}

/* Bouton historique - Bleu électrique PRIORITAIRE */
.btn-history {
  border-color: rgba(30, 144, 255, 0.7) !important;
  color: #1e90ff !important;
  background: linear-gradient(
    135deg,
    rgba(30, 144, 255, 0.25) 0%,
    rgba(0, 191, 255, 0.25) 50%,
    rgba(65, 105, 225, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(30, 144, 255, 0.6),
    inset 0 0 20px rgba(30, 144, 255, 0.2), 0 0 50px rgba(0, 191, 255, 0.3) !important;
  animation: pulseBlueElectric 2.8s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-history:hover {
  box-shadow: 0 0 50px rgba(30, 144, 255, 1), 0 0 100px rgba(0, 191, 255, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(30, 144, 255, 1) !important;
  border-color: rgba(30, 144, 255, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(2deg) !important;
  animation: pulseBlueElectric 0.8s ease-in-out infinite !important;
}

/* Bouton statistiques - Jaune électrique PRIORITAIRE */
.btn-stats {
  border-color: rgba(255, 215, 0, 0.7) !important;
  color: #ffd700 !important;
  background: linear-gradient(
    135deg,
    rgba(255, 215, 0, 0.25) 0%,
    rgba(255, 255, 0, 0.25) 50%,
    rgba(255, 193, 7, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(255, 215, 0, 0.6),
    inset 0 0 20px rgba(255, 215, 0, 0.2), 0 0 50px rgba(255, 255, 0, 0.3) !important;
  animation: pulseYellowElectric 2.3s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-stats:hover {
  box-shadow: 0 0 50px rgba(255, 215, 0, 1), 0 0 100px rgba(255, 255, 0, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(255, 215, 0, 1) !important;
  border-color: rgba(255, 215, 0, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(-2deg) !important;
  animation: pulseYellowElectric 0.8s ease-in-out infinite !important;
}

/* Bouton messages vocaux - Rose électrique PRIORITAIRE */
.btn-voice-messages {
  border-color: rgba(255, 20, 147, 0.7) !important;
  color: #ff1493 !important;
  background: linear-gradient(
    135deg,
    rgba(255, 20, 147, 0.25) 0%,
    rgba(255, 105, 180, 0.25) 50%,
    rgba(219, 112, 147, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(255, 20, 147, 0.6),
    inset 0 0 20px rgba(255, 20, 147, 0.2), 0 0 50px rgba(255, 105, 180, 0.3) !important;
  animation: pulsePinkElectric 2.7s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-voice-messages:hover {
  box-shadow: 0 0 50px rgba(255, 20, 147, 1), 0 0 100px rgba(255, 105, 180, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(255, 20, 147, 1) !important;
  border-color: rgba(255, 20, 147, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(3deg) !important;
  animation: pulsePinkElectric 0.8s ease-in-out infinite !important;
}

/* Bouton menu - Cyan électrique PRIORITAIRE */
.btn-menu {
  border-color: rgba(0, 255, 255, 0.7) !important;
  color: #00ffff !important;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.25) 0%,
    rgba(64, 224, 208, 0.25) 50%,
    rgba(0, 206, 209, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(0, 255, 255, 0.6),
    inset 0 0 20px rgba(0, 255, 255, 0.2), 0 0 50px rgba(64, 224, 208, 0.3) !important;
  animation: pulseCyanElectric 2.4s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
}

.btn-menu:hover {
  box-shadow: 0 0 50px rgba(0, 255, 255, 1), 0 0 100px rgba(64, 224, 208, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(0, 255, 255, 1) !important;
  border-color: rgba(0, 255, 255, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(-2deg) !important;
  animation: pulseCyanElectric 0.8s ease-in-out infinite !important;
}

/* ========================================
   NOUVELLES ICÔNES FUTURISTES - COULEURS HOLOGRAPHIQUES AVANCÉES
   ======================================== */

/* Bouton fermer/annuler - Rouge holographique PRIORITAIRE */
.btn-close,
.btn-cancel,
.close-btn {
  border-color: rgba(255, 0, 100, 0.8) !important;
  color: #ff0064 !important;
  background: linear-gradient(
    135deg,
    rgba(255, 0, 100, 0.3) 0%,
    rgba(255, 20, 147, 0.3) 50%,
    rgba(220, 20, 60, 0.3) 100%
  ) !important;
  box-shadow: 0 0 30px rgba(255, 0, 100, 0.7),
    inset 0 0 25px rgba(255, 0, 100, 0.2), 0 0 60px rgba(255, 20, 147, 0.4) !important;
  animation: pulseRedHolo 2.1s ease-in-out infinite !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
}

.btn-close:hover,
.btn-cancel:hover,
.close-btn:hover {
  box-shadow: 0 0 60px rgba(255, 0, 100, 1), 0 0 120px rgba(255, 20, 147, 0.8),
    inset 0 0 40px rgba(255, 255, 255, 0.4), 0 20px 50px rgba(0, 0, 0, 0.3) !important;
  color: #ffffff !important;
  text-shadow: 0 0 30px rgba(255, 0, 100, 1) !important;
  border-color: rgba(255, 0, 100, 1) !important;
  transform: scale(1.25) translateY(-6px) rotateZ(-3deg) !important;
  animation: pulseRedHolo 0.7s ease-in-out infinite !important;
}

/* Bouton confirmer/valider - Vert holographique PRIORITAIRE */
.btn-confirm,
.btn-validate,
.btn-ok {
  border-color: rgba(0, 255, 100, 0.8) !important;
  color: #00ff64 !important;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 100, 0.3) 0%,
    rgba(57, 255, 20, 0.3) 50%,
    rgba(0, 255, 127, 0.3) 100%
  ) !important;
  box-shadow: 0 0 30px rgba(0, 255, 100, 0.7),
    inset 0 0 25px rgba(0, 255, 100, 0.2), 0 0 60px rgba(57, 255, 20, 0.4) !important;
  animation: pulseGreenHolo 2.3s ease-in-out infinite !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
}

.btn-confirm:hover,
.btn-validate:hover,
.btn-ok:hover {
  box-shadow: 0 0 60px rgba(0, 255, 100, 1), 0 0 120px rgba(57, 255, 20, 0.8),
    inset 0 0 40px rgba(255, 255, 255, 0.4), 0 20px 50px rgba(0, 0, 0, 0.3) !important;
  color: #ffffff !important;
  text-shadow: 0 0 30px rgba(0, 255, 100, 1) !important;
  border-color: rgba(0, 255, 100, 1) !important;
  transform: scale(1.25) translateY(-6px) rotateZ(3deg) !important;
  animation: pulseGreenHolo 0.7s ease-in-out infinite !important;
}

/* Bouton éditer/modifier - Bleu holographique PRIORITAIRE */
.btn-edit,
.btn-modify,
.edit-btn {
  border-color: rgba(0, 150, 255, 0.8) !important;
  color: #0096ff !important;
  background: linear-gradient(
    135deg,
    rgba(0, 150, 255, 0.3) 0%,
    rgba(30, 144, 255, 0.3) 50%,
    rgba(0, 191, 255, 0.3) 100%
  ) !important;
  box-shadow: 0 0 30px rgba(0, 150, 255, 0.7),
    inset 0 0 25px rgba(0, 150, 255, 0.2), 0 0 60px rgba(30, 144, 255, 0.4) !important;
  animation: pulseBlueHolo 2.6s ease-in-out infinite !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
}

.btn-edit:hover,
.btn-modify:hover,
.edit-btn:hover {
  box-shadow: 0 0 60px rgba(0, 150, 255, 1), 0 0 120px rgba(30, 144, 255, 0.8),
    inset 0 0 40px rgba(255, 255, 255, 0.4), 0 20px 50px rgba(0, 0, 0, 0.3) !important;
  color: #ffffff !important;
  text-shadow: 0 0 30px rgba(0, 150, 255, 1) !important;
  border-color: rgba(0, 150, 255, 1) !important;
  transform: scale(1.25) translateY(-6px) rotateZ(-2deg) !important;
  animation: pulseBlueHolo 0.7s ease-in-out infinite !important;
}

/* Bouton supprimer/effacer - Orange holographique PRIORITAIRE */
.btn-delete,
.btn-remove,
.delete-btn {
  border-color: rgba(255, 100, 0, 0.8) !important;
  color: #ff6400 !important;
  background: linear-gradient(
    135deg,
    rgba(255, 100, 0, 0.3) 0%,
    rgba(255, 69, 0, 0.3) 50%,
    rgba(255, 140, 0, 0.3) 100%
  ) !important;
  box-shadow: 0 0 30px rgba(255, 100, 0, 0.7),
    inset 0 0 25px rgba(255, 100, 0, 0.2), 0 0 60px rgba(255, 69, 0, 0.4) !important;
  animation: pulseOrangeHolo 2.4s ease-in-out infinite !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
}

.btn-delete:hover,
.btn-remove:hover,
.delete-btn:hover {
  box-shadow: 0 0 60px rgba(255, 100, 0, 1), 0 0 120px rgba(255, 69, 0, 0.8),
    inset 0 0 40px rgba(255, 255, 255, 0.4), 0 20px 50px rgba(0, 0, 0, 0.3) !important;
  color: #ffffff !important;
  text-shadow: 0 0 30px rgba(255, 100, 0, 1) !important;
  border-color: rgba(255, 100, 0, 1) !important;
  transform: scale(1.25) translateY(-6px) rotateZ(4deg) !important;
  animation: pulseOrangeHolo 0.7s ease-in-out infinite !important;
}

/* ========================================
   BOUTONS DE RÉACTION ÉMOJIS - HOLOGRAPHIQUES
   ======================================== */

/* Boutons de réaction avec effets holographiques */
.reaction-picker button,
.emoji-reaction-btn,
.futuristic-message button[class*="flex items-center space-x-1"] {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(0, 255, 255, 0.15) 50%,
    rgba(255, 0, 255, 0.15) 100%
  ) !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 20px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.reaction-picker button:hover,
.emoji-reaction-btn:hover,
.futuristic-message button[class*="flex items-center space-x-1"]:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(0, 255, 255, 0.3) 50%,
    rgba(255, 0, 255, 0.3) 100%
  ) !important;
  transform: scale(1.1) translateY(-2px) !important;
  box-shadow: 0 0 25px rgba(255, 255, 255, 0.4), 0 0 50px rgba(0, 255, 255, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.5) !important;
  border-color: rgba(0, 255, 255, 0.6) !important;
}

/* ========================================
   BOUTONS DE LECTURE VOCALE - PLASMA AUDIO
   ======================================== */

/* Bouton de lecture/pause pour messages vocaux */
.voice-play-btn-modern,
.play-button,
.voice-message-modern .play-button {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 127, 0.9) 0%,
    rgba(57, 255, 20, 0.9) 50%,
    rgba(0, 255, 0, 0.9) 100%
  ) !important;
  border: 2px solid rgba(0, 255, 127, 0.8) !important;
  color: #ffffff !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 0 20px rgba(0, 255, 127, 0.6),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
  animation: pulseAudioGreen 2s ease-in-out infinite !important;
}

.voice-play-btn-modern:hover,
.play-button:hover,
.voice-message-modern .play-button:hover {
  transform: scale(1.2) translateY(-3px) !important;
  box-shadow: 0 0 40px rgba(0, 255, 127, 1), 0 0 80px rgba(57, 255, 20, 0.8),
    inset 0 0 20px rgba(255, 255, 255, 0.4) !important;
  border-color: rgba(0, 255, 127, 1) !important;
  animation: pulseAudioGreen 0.8s ease-in-out infinite !important;
}

/* État pause pour les boutons audio */
.voice-play-btn-modern.playing,
.play-button.playing {
  background: linear-gradient(
    135deg,
    rgba(255, 69, 0, 0.9) 0%,
    rgba(255, 20, 147, 0.9) 50%,
    rgba(220, 38, 127, 0.9) 100%
  ) !important;
  border-color: rgba(255, 69, 0, 0.8) !important;
  box-shadow: 0 0 20px rgba(255, 69, 0, 0.6),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  animation: pulseAudioRed 1.5s ease-in-out infinite !important;
}

/* ========================================
   BOUTONS D'OPTIONS DE MESSAGE - FUTURISTES
   ======================================== */

/* Boutons d'options dans les messages (répondre, transférer, etc.) */
.futuristic-message-bubble button[class*="w-full px-3 py-2"],
.message-options-btn {
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.1) 0%,
    rgba(255, 20, 147, 0.1) 100%
  ) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
  color: #00f7ff !important;
  border-radius: 12px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) saturate(120%) !important;
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.futuristic-message-bubble button[class*="w-full px-3 py-2"]:hover,
.message-options-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.2) 0%,
    rgba(255, 20, 147, 0.2) 100%
  ) !important;
  transform: translateX(3px) !important;
  border-color: rgba(0, 247, 255, 0.6) !important;
  box-shadow: 0 0 20px rgba(0, 247, 255, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.2) !important;
  text-shadow: 0 0 8px rgba(0, 247, 255, 0.8) !important;
}

/* Couleurs spécialisées pour chaque action */
.futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:nth-child(1):hover {
  color: #39ff14 !important;
  border-color: rgba(57, 255, 20, 0.6) !important;
  text-shadow: 0 0 8px rgba(57, 255, 20, 0.8) !important;
  box-shadow: 0 0 20px rgba(57, 255, 20, 0.4) !important;
}

.futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:nth-child(2):hover {
  color: #ff1493 !important;
  border-color: rgba(255, 20, 147, 0.6) !important;
  text-shadow: 0 0 8px rgba(255, 20, 147, 0.8) !important;
  box-shadow: 0 0 20px rgba(255, 20, 147, 0.4) !important;
}

.futuristic-message-bubble
  button[class*="w-full px-3 py-2"]:nth-child(3):hover {
  color: #ffd700 !important;
  border-color: rgba(255, 215, 0, 0.6) !important;
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.8) !important;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.4) !important;
}

/* ========================================
   CORRECTIONS SPÉCIFIQUES - ICÔNES MANQUANTES
   ======================================== */

/* Forcer les couleurs pour l'icône messages épinglés */
.whatsapp-action-button.btn-pinned,
button.whatsapp-action-button.btn-pinned,
.whatsapp-actions .btn-pinned,
.theme-default .whatsapp-action-button.btn-pinned,
.theme-feminine .whatsapp-action-button.btn-pinned,
.theme-masculine .whatsapp-action-button.btn-pinned,
.theme-neutral .whatsapp-action-button.btn-pinned {
  /* Forcer le violet holographique TOUJOURS */
  border-color: rgba(138, 43, 226, 0.7) !important;
  color: #8a2be2 !important;
  background: linear-gradient(
    135deg,
    rgba(138, 43, 226, 0.25) 0%,
    rgba(75, 0, 130, 0.25) 50%,
    rgba(148, 0, 211, 0.25) 100%
  ) !important;
  box-shadow: 0 0 25px rgba(138, 43, 226, 0.6),
    inset 0 0 20px rgba(138, 43, 226, 0.2), 0 0 50px rgba(75, 0, 130, 0.3) !important;
  animation: pulseViolet 2.5s ease-in-out infinite !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
  text-shadow: 0 0 15px rgba(138, 43, 226, 0.9) !important;
}

/* État de survol pour l'icône messages épinglés */
.whatsapp-action-button.btn-pinned:hover,
button.whatsapp-action-button.btn-pinned:hover,
.whatsapp-actions .btn-pinned:hover,
.theme-default .whatsapp-action-button.btn-pinned:hover,
.theme-feminine .whatsapp-action-button.btn-pinned:hover,
.theme-masculine .whatsapp-action-button.btn-pinned:hover,
.theme-neutral .whatsapp-action-button.btn-pinned:hover {
  box-shadow: 0 0 50px rgba(138, 43, 226, 1), 0 0 100px rgba(75, 0, 130, 0.8),
    inset 0 0 30px rgba(255, 255, 255, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(138, 43, 226, 1) !important;
  border-color: rgba(138, 43, 226, 1) !important;
  transform: scale(1.2) translateY(-5px) rotateZ(2deg) !important;
  animation: pulseViolet 0.8s ease-in-out infinite !important;
}

/* Flèches de navigation - Style holographique */
.futuristic-message-other-user .futuristic-message-bubble::before,
.futuristic-message-bubble::before {
  /* Flèche avec effet holographique */
  border-right-color: rgba(0, 255, 255, 0.8) !important;
  filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.6)) !important;
  animation: pulseArrow 3s ease-in-out infinite !important;
}

.dark .futuristic-message-other-user .futuristic-message-bubble::before,
.dark .futuristic-message-bubble::before {
  border-right-color: rgba(0, 255, 255, 0.9) !important;
  filter: drop-shadow(0 0 12px rgba(0, 255, 255, 0.8)) !important;
}

/* Flèche vers la droite pour messages utilisateur */
.futuristic-message-current-user .futuristic-message-bubble::after {
  content: "";
  position: absolute;
  top: 0;
  right: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-left: 8px solid rgba(255, 20, 147, 0.8) !important;
  border-bottom: 8px solid transparent;
  z-index: 1;
  filter: drop-shadow(2px 0px 8px rgba(255, 20, 147, 0.6)) !important;
  animation: pulseArrowRight 3s ease-in-out infinite !important;
}

.dark .futuristic-message-current-user .futuristic-message-bubble::after {
  border-left-color: rgba(255, 20, 147, 0.9) !important;
  filter: drop-shadow(2px 0px 12px rgba(255, 20, 147, 0.8)) !important;
}

/* Animations pour les flèches */
@keyframes pulseArrow {
  0%,
  100% {
    filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(0, 255, 255, 0.9));
  }
}

@keyframes pulseArrowRight {
  0%,
  100% {
    filter: drop-shadow(2px 0px 8px rgba(255, 20, 147, 0.6));
  }
  50% {
    filter: drop-shadow(2px 0px 15px rgba(255, 20, 147, 0.9));
  }
}

/* ========================================
   CORRECTIONS BOUTONS VOCAUX - COULEURS PLASMA
   ======================================== */

/* Forcer les couleurs pour le bouton d'enregistrement vocal */
.whatsapp-voice-button,
button.whatsapp-voice-button,
.whatsapp-input-container .whatsapp-voice-button,
.theme-default .whatsapp-voice-button,
.theme-feminine .whatsapp-voice-button,
.theme-masculine .whatsapp-voice-button,
.theme-neutral .whatsapp-voice-button {
  /* Forcer le plasma rouge-orange TOUJOURS */
  width: 46px !important;
  height: 46px !important;
  border-radius: 50% !important;
  background: linear-gradient(
    135deg,
    rgba(255, 69, 0, 0.9) 0%,
    rgba(255, 20, 147, 0.9) 50%,
    rgba(220, 38, 127, 0.9) 100%
  ) !important;
  border: 2px solid rgba(255, 69, 0, 0.7) !important;
  color: #ffffff !important;
  box-shadow: 0 0 25px rgba(255, 69, 0, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
  animation: gradientShift 3s ease infinite !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-size: 1.1rem !important;
}

/* État de survol pour le bouton vocal */
.whatsapp-voice-button:hover,
button.whatsapp-voice-button:hover,
.whatsapp-input-container .whatsapp-voice-button:hover,
.theme-default .whatsapp-voice-button:hover,
.theme-feminine .whatsapp-voice-button:hover,
.theme-masculine .whatsapp-voice-button:hover,
.theme-neutral .whatsapp-voice-button:hover {
  transform: scale(1.15) translateY(-3px) !important;
  box-shadow: 0 0 40px rgba(255, 69, 0, 0.8), 0 0 80px rgba(255, 20, 147, 0.6),
    inset 0 0 20px rgba(255, 255, 255, 0.4) !important;
  border-color: rgba(255, 69, 0, 1) !important;
  animation: pulseMagenta 1s ease-in-out infinite !important;
}

/* État d'enregistrement actif */
.whatsapp-voice-button.recording,
button.whatsapp-voice-button.recording {
  animation: pulseMagenta 1s ease-in-out infinite !important;
  box-shadow: 0 0 30px rgba(255, 69, 0, 0.9), 0 0 60px rgba(255, 20, 147, 0.6) !important;
  border-color: rgba(255, 20, 147, 1) !important;
}

/* Forcer les couleurs pour les boutons de lecture vocale */
.voice-play-btn-modern,
.play-button,
.voice-message-modern .play-button,
app-voice-message-player .play-button,
.futuristic-message app-voice-message-player .play-button,
.theme-default .voice-play-btn-modern,
.theme-feminine .voice-play-btn-modern,
.theme-masculine .voice-play-btn-modern,
.theme-neutral .voice-play-btn-modern,
.theme-default .play-button,
.theme-feminine .play-button,
.theme-masculine .play-button,
.theme-neutral .play-button {
  /* Forcer le vert plasma TOUJOURS */
  background: linear-gradient(
    135deg,
    rgba(0, 255, 127, 0.9) 0%,
    rgba(57, 255, 20, 0.9) 50%,
    rgba(0, 255, 0, 0.9) 100%
  ) !important;
  border: 2px solid rgba(0, 255, 127, 0.8) !important;
  color: #ffffff !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 0 20px rgba(0, 255, 127, 0.6),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
  animation: pulseAudioGreen 2s ease-in-out infinite !important;
}

/* État de survol pour les boutons de lecture */
.voice-play-btn-modern:hover,
.play-button:hover,
.voice-message-modern .play-button:hover,
app-voice-message-player .play-button:hover,
.futuristic-message app-voice-message-player .play-button:hover,
.theme-default .voice-play-btn-modern:hover,
.theme-feminine .voice-play-btn-modern:hover,
.theme-masculine .voice-play-btn-modern:hover,
.theme-neutral .voice-play-btn-modern:hover,
.theme-default .play-button:hover,
.theme-feminine .play-button:hover,
.theme-masculine .play-button:hover,
.theme-neutral .play-button:hover {
  transform: scale(1.2) translateY(-3px) !important;
  box-shadow: 0 0 40px rgba(0, 255, 127, 1), 0 0 80px rgba(57, 255, 20, 0.8),
    inset 0 0 20px rgba(255, 255, 255, 0.4) !important;
  border-color: rgba(0, 255, 127, 1) !important;
  animation: pulseAudioGreen 0.8s ease-in-out infinite !important;
}

/* État pause pour les boutons audio */
.voice-play-btn-modern.playing,
.play-button.playing,
app-voice-message-player .play-button.playing {
  background: linear-gradient(
    135deg,
    rgba(255, 69, 0, 0.9) 0%,
    rgba(255, 20, 147, 0.9) 50%,
    rgba(220, 38, 127, 0.9) 100%
  ) !important;
  border-color: rgba(255, 69, 0, 0.8) !important;
  box-shadow: 0 0 20px rgba(255, 69, 0, 0.6),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  animation: pulseAudioRed 1.5s ease-in-out infinite !important;
}

/* ========================================
   BOUTON THÈME - ARC-EN-CIEL HOLOGRAPHIQUE ULTRA PRIORITAIRE
   ======================================== */

/* Forcer l'affichage du bouton thème avec spécificité maximale */
.whatsapp-action-button.btn-theme,
button.whatsapp-action-button.btn-theme,
.whatsapp-actions .btn-theme {
  /* Forcer la visibilité */
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;

  /* Dimensions garanties */
  width: 42px !important;
  height: 42px !important;
  min-width: 42px !important;
  min-height: 42px !important;

  /* Couleurs arc-en-ciel ultra visibles */
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  color: #ffffff !important;
  background: linear-gradient(
    45deg,
    rgba(255, 0, 0, 0.6) 0%,
    /* Rouge intense */ rgba(255, 165, 0, 0.6) 14%,
    /* Orange intense */ rgba(255, 255, 0, 0.6) 28%,
    /* Jaune intense */ rgba(0, 255, 0, 0.6) 42%,
    /* Vert intense */ rgba(0, 255, 255, 0.6) 56%,
    /* Cyan intense */ rgba(0, 0, 255, 0.6) 70%,
    /* Bleu intense */ rgba(75, 0, 130, 0.6) 84%,
    /* Indigo intense */ rgba(238, 130, 238, 0.6) 100% /* Violet intense */
  ) !important;

  /* Effets lumineux garantis */
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.6),
    inset 0 0 25px rgba(255, 255, 255, 0.3), 0 0 60px rgba(255, 0, 255, 0.4) !important;

  /* Animations multiples */
  animation: gradientShift 3s ease-in-out infinite,
    pulseRainbow 2s ease-in-out infinite !important;

  /* Text shadow intense */
  text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;

  /* Positionnement et layout */
  position: relative !important;
  z-index: 10 !important;
  border-radius: 50% !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* État de survol ultra intense */
.whatsapp-action-button.btn-theme:hover,
button.whatsapp-action-button.btn-theme:hover,
.whatsapp-actions .btn-theme:hover {
  /* Transformation 3D */
  transform: scale(1.25) translateY(-5px) rotateZ(5deg) !important;

  /* Lueurs ultra intenses */
  box-shadow: 0 0 60px rgba(255, 255, 255, 1),
    0 0 120px rgba(255, 255, 255, 0.6), inset 0 0 40px rgba(255, 255, 255, 0.5),
    0 0 100px rgba(255, 0, 255, 0.8), 0 15px 40px rgba(0, 0, 0, 0.3) !important;

  /* Couleurs encore plus intenses */
  border-color: rgba(255, 255, 255, 1) !important;
  background: linear-gradient(
    45deg,
    rgba(255, 0, 0, 0.9) 0%,
    rgba(255, 165, 0, 0.9) 14%,
    rgba(255, 255, 0, 0.9) 28%,
    rgba(0, 255, 0, 0.9) 42%,
    rgba(0, 255, 255, 0.9) 56%,
    rgba(0, 0, 255, 0.9) 70%,
    rgba(75, 0, 130, 0.9) 84%,
    rgba(238, 130, 238, 0.9) 100%
  ) !important;

  /* Animations accélérées */
  animation: rotateHalo 0.8s linear infinite,
    gradientShift 1.5s ease-in-out infinite,
    pulseRainbow 1s ease-in-out infinite !important;

  /* Text shadow ultra intense */
  text-shadow: 0 0 30px rgba(255, 255, 255, 1),
    0 0 50px rgba(255, 255, 255, 0.8) !important;
}

/* ========================================
   NOUVELLES ANIMATIONS POUR COULEURS MODERNES
   ======================================== */

/* Animation vert laser holographique */
@keyframes pulseGreenLaser {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(0, 255, 127, 0.6),
      inset 0 0 20px rgba(0, 255, 127, 0.2), 0 0 50px rgba(57, 255, 20, 0.3);
  }
  50% {
    filter: hue-rotate(30deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 35px rgba(0, 255, 127, 0.8),
      inset 0 0 25px rgba(0, 255, 127, 0.3), 0 0 70px rgba(57, 255, 20, 0.5);
  }
}

/* Animation plasma magenta-cyan */
@keyframes pulseMagentaCyan {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(255, 0, 255, 0.6),
      inset 0 0 20px rgba(255, 0, 255, 0.2), 0 0 50px rgba(0, 255, 255, 0.3);
  }
  33% {
    filter: hue-rotate(60deg) saturate(1.2) brightness(1.1);
    box-shadow: 0 0 30px rgba(255, 0, 255, 0.7),
      inset 0 0 22px rgba(255, 0, 255, 0.25), 0 0 60px rgba(0, 255, 255, 0.4);
  }
  66% {
    filter: hue-rotate(-60deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 35px rgba(0, 255, 255, 0.8),
      inset 0 0 25px rgba(0, 255, 255, 0.3), 0 0 70px rgba(255, 0, 255, 0.5);
  }
}

/* Animation or électrique */
@keyframes pulseGold {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.6),
      inset 0 0 20px rgba(255, 215, 0, 0.2), 0 0 50px rgba(255, 255, 0, 0.3);
  }
  50% {
    filter: hue-rotate(15deg) saturate(1.4) brightness(1.3);
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.9),
      inset 0 0 30px rgba(255, 215, 0, 0.4), 0 0 80px rgba(255, 255, 0, 0.6);
  }
}

/* Animation plasma rouge-orange */
@keyframes pulseRedOrange {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(255, 69, 0, 0.6),
      inset 0 0 20px rgba(255, 69, 0, 0.2), 0 0 50px rgba(255, 0, 0, 0.3);
  }
  50% {
    filter: hue-rotate(20deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 35px rgba(255, 69, 0, 0.8),
      inset 0 0 25px rgba(255, 69, 0, 0.3), 0 0 70px rgba(255, 0, 0, 0.5);
  }
}

/* Animation violet holographique */
@keyframes pulseViolet {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(138, 43, 226, 0.6),
      inset 0 0 20px rgba(138, 43, 226, 0.2), 0 0 50px rgba(75, 0, 130, 0.3);
  }
  50% {
    filter: hue-rotate(30deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 35px rgba(138, 43, 226, 0.8),
      inset 0 0 25px rgba(138, 43, 226, 0.3), 0 0 70px rgba(75, 0, 130, 0.5);
  }
}

/* Animation émeraude électrique */
@keyframes pulseEmerald {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(0, 255, 146, 0.6),
      inset 0 0 20px rgba(0, 255, 146, 0.2), 0 0 50px rgba(0, 255, 127, 0.3);
  }
  50% {
    filter: hue-rotate(20deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 35px rgba(0, 255, 146, 0.8),
      inset 0 0 25px rgba(0, 255, 146, 0.3), 0 0 70px rgba(0, 255, 127, 0.5);
  }
}

/* Animation bleu électrique */
@keyframes pulseBlueElectric {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(30, 144, 255, 0.6),
      inset 0 0 20px rgba(30, 144, 255, 0.2), 0 0 50px rgba(0, 191, 255, 0.3);
  }
  50% {
    filter: hue-rotate(15deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 35px rgba(30, 144, 255, 0.8),
      inset 0 0 25px rgba(30, 144, 255, 0.3), 0 0 70px rgba(0, 191, 255, 0.5);
  }
}

/* Animation jaune électrique */
@keyframes pulseYellowElectric {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.6),
      inset 0 0 20px rgba(255, 215, 0, 0.2), 0 0 50px rgba(255, 255, 0, 0.3);
  }
  50% {
    filter: hue-rotate(10deg) saturate(1.4) brightness(1.3);
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.9),
      inset 0 0 30px rgba(255, 215, 0, 0.4), 0 0 80px rgba(255, 255, 0, 0.6);
  }
}

/* Animation rose électrique */
@keyframes pulsePinkElectric {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(255, 20, 147, 0.6),
      inset 0 0 20px rgba(255, 20, 147, 0.2), 0 0 50px rgba(255, 105, 180, 0.3);
  }
  50% {
    filter: hue-rotate(20deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 35px rgba(255, 20, 147, 0.8),
      inset 0 0 25px rgba(255, 20, 147, 0.3), 0 0 70px rgba(255, 105, 180, 0.5);
  }
}

/* Animation cyan électrique */
@keyframes pulseCyanElectric {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 25px rgba(0, 255, 255, 0.6),
      inset 0 0 20px rgba(0, 255, 255, 0.2), 0 0 50px rgba(64, 224, 208, 0.3);
  }
  50% {
    filter: hue-rotate(15deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 35px rgba(0, 255, 255, 0.8),
      inset 0 0 25px rgba(0, 255, 255, 0.3), 0 0 70px rgba(64, 224, 208, 0.5);
  }
}

/* ========================================
   NOUVELLES ANIMATIONS HOLOGRAPHIQUES AVANCÉES
   ======================================== */

/* Animation rouge holographique */
@keyframes pulseRedHolo {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 30px rgba(255, 0, 100, 0.7),
      inset 0 0 25px rgba(255, 0, 100, 0.2), 0 0 60px rgba(255, 20, 147, 0.4);
  }
  50% {
    filter: hue-rotate(10deg) saturate(1.4) brightness(1.3);
    box-shadow: 0 0 45px rgba(255, 0, 100, 0.9),
      inset 0 0 35px rgba(255, 0, 100, 0.3), 0 0 90px rgba(255, 20, 147, 0.6);
  }
}

/* Animation vert holographique */
@keyframes pulseGreenHolo {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 30px rgba(0, 255, 100, 0.7),
      inset 0 0 25px rgba(0, 255, 100, 0.2), 0 0 60px rgba(57, 255, 20, 0.4);
  }
  50% {
    filter: hue-rotate(20deg) saturate(1.4) brightness(1.3);
    box-shadow: 0 0 45px rgba(0, 255, 100, 0.9),
      inset 0 0 35px rgba(0, 255, 100, 0.3), 0 0 90px rgba(57, 255, 20, 0.6);
  }
}

/* Animation bleu holographique */
@keyframes pulseBlueHolo {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 30px rgba(0, 150, 255, 0.7),
      inset 0 0 25px rgba(0, 150, 255, 0.2), 0 0 60px rgba(30, 144, 255, 0.4);
  }
  50% {
    filter: hue-rotate(15deg) saturate(1.4) brightness(1.3);
    box-shadow: 0 0 45px rgba(0, 150, 255, 0.9),
      inset 0 0 35px rgba(0, 150, 255, 0.3), 0 0 90px rgba(30, 144, 255, 0.6);
  }
}

/* Animation orange holographique */
@keyframes pulseOrangeHolo {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 30px rgba(255, 100, 0, 0.7),
      inset 0 0 25px rgba(255, 100, 0, 0.2), 0 0 60px rgba(255, 69, 0, 0.4);
  }
  50% {
    filter: hue-rotate(15deg) saturate(1.4) brightness(1.3);
    box-shadow: 0 0 45px rgba(255, 100, 0, 0.9),
      inset 0 0 35px rgba(255, 100, 0, 0.3), 0 0 90px rgba(255, 69, 0, 0.6);
  }
}

/* Animation audio vert */
@keyframes pulseAudioGreen {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 20px rgba(0, 255, 127, 0.6),
      inset 0 2px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    filter: hue-rotate(30deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 30px rgba(0, 255, 127, 0.8),
      inset 0 3px 0 rgba(255, 255, 255, 0.4);
  }
}

/* Animation audio rouge */
@keyframes pulseAudioRed {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
    box-shadow: 0 0 20px rgba(255, 69, 0, 0.6),
      inset 0 2px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    filter: hue-rotate(20deg) saturate(1.3) brightness(1.2);
    box-shadow: 0 0 30px rgba(255, 69, 0, 0.8),
      inset 0 3px 0 rgba(255, 255, 255, 0.4);
  }
}

/* Animation arc-en-ciel pulsante */
@keyframes pulseRainbow {
  0%,
  100% {
    filter: hue-rotate(0deg) saturate(1);
  }
  25% {
    filter: hue-rotate(90deg) saturate(1.2);
  }
  50% {
    filter: hue-rotate(180deg) saturate(1.4);
  }
  75% {
    filter: hue-rotate(270deg) saturate(1.2);
  }
}

/* ========================================
   MENU DÉROULANT DES THÈMES - STYLES FLUORESCENTS
   ======================================== */

/* Conteneur du menu des thèmes */
.theme-selector-menu {
  /* Forcer la visibilité */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 100 !important;

  /* Effets visuels futuristes */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 240, 240, 0.95) 100%
  ) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  border: 2px solid rgba(0, 247, 255, 0.3) !important;
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.2), 0 10px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;

  /* Animation d'apparition */
  animation: fadeIn 0.3s ease-out !important;
  transform-origin: top right !important;
}

/* Mode sombre pour le menu des thèmes */
.dark .theme-selector-menu {
  background: linear-gradient(
    135deg,
    rgba(30, 30, 30, 0.95) 0%,
    rgba(20, 20, 20, 0.95) 100%
  ) !important;
  border-color: rgba(0, 247, 255, 0.4) !important;
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.3), 0 10px 40px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* Options de thème individuelles */
.theme-selector-menu a {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 8px !important;
  margin: 2px !important;
}

/* Effets de survol pour les options de thème */
.theme-selector-menu a:hover {
  transform: translateX(5px) !important;
  background: linear-gradient(
    90deg,
    rgba(0, 247, 255, 0.1) 0%,
    rgba(255, 20, 147, 0.1) 100%
  ) !important;
  border-left: 3px solid rgba(0, 247, 255, 0.6) !important;
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.2) !important;
}

/* Cercles de couleur des thèmes avec effets fluorescents */
.theme-selector-menu .w-4.h-4.rounded-full {
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  transition: all 0.2s ease !important;
}

/* Effets de survol pour les cercles de couleur */
.theme-selector-menu a:hover .w-4.h-4.rounded-full {
  transform: scale(1.2) !important;
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
  border-color: rgba(0, 247, 255, 0.6) !important;
}

/* Thème par défaut - Bleu électrique */
.theme-selector-menu a:hover .bg-\[\#4f5fad\] {
  background: linear-gradient(45deg, #4f5fad 0%, #6d78c9 100%) !important;
  box-shadow: 0 0 20px rgba(79, 95, 173, 0.6) !important;
}

/* Thème rose - Magenta électrique */
.theme-selector-menu a:hover .bg-\[\#ff6b9d\] {
  background: linear-gradient(45deg, #ff6b9d 0%, #ff1493 100%) !important;
  box-shadow: 0 0 20px rgba(255, 107, 157, 0.6) !important;
}

/* Thème bleu - Cyan électrique */
.theme-selector-menu a:hover .bg-\[\#3d85c6\] {
  background: linear-gradient(45deg, #3d85c6 0%, #00bfff 100%) !important;
  box-shadow: 0 0 20px rgba(61, 133, 198, 0.6) !important;
}

/* Thème vert - Vert néon */
.theme-selector-menu a:hover .bg-\[\#6aa84f\] {
  background: linear-gradient(45deg, #6aa84f 0%, #39ff14 100%) !important;
  box-shadow: 0 0 20px rgba(106, 168, 79, 0.6) !important;
}

/* ========================================
   STYLES DES THÈMES - APPLICATION RÉELLE
   ======================================== */

/* THÈME PAR DÉFAUT - Bleu électrique */
.theme-default {
  --theme-primary: #4f5fad;
  --theme-primary-light: #6d78c9;
  --theme-primary-dark: #3d4a85;
  --theme-accent: rgba(79, 95, 173, 0.1);
  --theme-border: rgba(79, 95, 173, 0.3);
  --theme-glow: rgba(79, 95, 173, 0.5);
}

.theme-default .whatsapp-chat-header {
  background: linear-gradient(
    135deg,
    rgba(79, 95, 173, 0.95) 0%,
    rgba(109, 120, 201, 0.95) 100%
  ) !important;
  border-bottom: 2px solid var(--theme-border) !important;
  box-shadow: 0 0 20px var(--theme-glow) !important;
}

.theme-default .whatsapp-input-container {
  background: linear-gradient(
    135deg,
    rgba(79, 95, 173, 0.1) 0%,
    rgba(109, 120, 201, 0.1) 100%
  ) !important;
  border-top: 2px solid var(--theme-border) !important;
  box-shadow: 0 0 15px var(--theme-glow) !important;
}

.theme-default .futuristic-messages-container {
  background: linear-gradient(
    135deg,
    rgba(79, 95, 173, 0.05) 0%,
    rgba(109, 120, 201, 0.05) 100%
  ) !important;
}

/* THÈME ROSE - Magenta électrique */
.theme-feminine {
  --theme-primary: #ff6b9d;
  --theme-primary-light: #ff1493;
  --theme-primary-dark: #dc2777;
  --theme-accent: rgba(255, 107, 157, 0.1);
  --theme-border: rgba(255, 107, 157, 0.3);
  --theme-glow: rgba(255, 107, 157, 0.5);
}

.theme-feminine .whatsapp-chat-header {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 157, 0.95) 0%,
    rgba(255, 20, 147, 0.95) 100%
  ) !important;
  border-bottom: 2px solid var(--theme-border) !important;
  box-shadow: 0 0 20px var(--theme-glow) !important;
}

.theme-feminine .whatsapp-input-container {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 157, 0.1) 0%,
    rgba(255, 20, 147, 0.1) 100%
  ) !important;
  border-top: 2px solid var(--theme-border) !important;
  box-shadow: 0 0 15px var(--theme-glow) !important;
}

.theme-feminine .futuristic-messages-container {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 157, 0.05) 0%,
    rgba(255, 20, 147, 0.05) 100%
  ) !important;
}

/* THÈME BLEU - Cyan électrique */
.theme-masculine {
  --theme-primary: #3d85c6;
  --theme-primary-light: #00bfff;
  --theme-primary-dark: #1e40af;
  --theme-accent: rgba(61, 133, 198, 0.1);
  --theme-border: rgba(61, 133, 198, 0.3);
  --theme-glow: rgba(61, 133, 198, 0.5);
}

.theme-masculine .whatsapp-chat-header {
  background: linear-gradient(
    135deg,
    rgba(61, 133, 198, 0.95) 0%,
    rgba(0, 191, 255, 0.95) 100%
  ) !important;
  border-bottom: 2px solid var(--theme-border) !important;
  box-shadow: 0 0 20px var(--theme-glow) !important;
}

.theme-masculine .whatsapp-input-container {
  background: linear-gradient(
    135deg,
    rgba(61, 133, 198, 0.1) 0%,
    rgba(0, 191, 255, 0.1) 100%
  ) !important;
  border-top: 2px solid var(--theme-border) !important;
  box-shadow: 0 0 15px var(--theme-glow) !important;
}

.theme-masculine .futuristic-messages-container {
  background: linear-gradient(
    135deg,
    rgba(61, 133, 198, 0.05) 0%,
    rgba(0, 191, 255, 0.05) 100%
  ) !important;
}

/* THÈME VERT - Vert néon */
.theme-neutral {
  --theme-primary: #6aa84f;
  --theme-primary-light: #39ff14;
  --theme-primary-dark: #16a34a;
  --theme-accent: rgba(106, 168, 79, 0.1);
  --theme-border: rgba(106, 168, 79, 0.3);
  --theme-glow: rgba(106, 168, 79, 0.5);
}

.theme-neutral .whatsapp-chat-header {
  background: linear-gradient(
    135deg,
    rgba(106, 168, 79, 0.95) 0%,
    rgba(57, 255, 20, 0.95) 100%
  ) !important;
  border-bottom: 2px solid var(--theme-border) !important;
  box-shadow: 0 0 20px var(--theme-glow) !important;
}

.theme-neutral .whatsapp-input-container {
  background: linear-gradient(
    135deg,
    rgba(106, 168, 79, 0.1) 0%,
    rgba(57, 255, 20, 0.1) 100%
  ) !important;
  border-top: 2px solid var(--theme-border) !important;
  box-shadow: 0 0 15px var(--theme-glow) !important;
}

.theme-neutral .futuristic-messages-container {
  background: linear-gradient(
    135deg,
    rgba(106, 168, 79, 0.05) 0%,
    rgba(57, 255, 20, 0.05) 100%
  ) !important;
}

/* ========================================
   STYLES DES MESSAGES PAR THÈME
   ======================================== */

/* Messages utilisateur (droite) pour chaque thème */
.theme-default .futuristic-message.sent {
  background: linear-gradient(
    135deg,
    rgba(79, 95, 173, 0.9) 0%,
    rgba(109, 120, 201, 0.9) 100%
  ) !important;
  border: 1px solid rgba(79, 95, 173, 0.3) !important;
  box-shadow: 0 0 15px rgba(79, 95, 173, 0.3) !important;
}

.theme-feminine .futuristic-message.sent {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 157, 0.9) 0%,
    rgba(255, 20, 147, 0.9) 100%
  ) !important;
  border: 1px solid rgba(255, 107, 157, 0.3) !important;
  box-shadow: 0 0 15px rgba(255, 107, 157, 0.3) !important;
}

.theme-masculine .futuristic-message.sent {
  background: linear-gradient(
    135deg,
    rgba(61, 133, 198, 0.9) 0%,
    rgba(0, 191, 255, 0.9) 100%
  ) !important;
  border: 1px solid rgba(61, 133, 198, 0.3) !important;
  box-shadow: 0 0 15px rgba(61, 133, 198, 0.3) !important;
}

.theme-neutral .futuristic-message.sent {
  background: linear-gradient(
    135deg,
    rgba(106, 168, 79, 0.9) 0%,
    rgba(57, 255, 20, 0.9) 100%
  ) !important;
  border: 1px solid rgba(106, 168, 79, 0.3) !important;
  box-shadow: 0 0 15px rgba(106, 168, 79, 0.3) !important;
}

/* Messages reçus (gauche) - style uniforme mais avec accent du thème */
.theme-default .futuristic-message.received {
  border-left: 3px solid var(--theme-primary) !important;
  box-shadow: 0 0 10px var(--theme-glow) !important;
}

.theme-feminine .futuristic-message.received {
  border-left: 3px solid var(--theme-primary) !important;
  box-shadow: 0 0 10px var(--theme-glow) !important;
}

.theme-masculine .futuristic-message.received {
  border-left: 3px solid var(--theme-primary) !important;
  box-shadow: 0 0 10px var(--theme-glow) !important;
}

.theme-neutral .futuristic-message.received {
  border-left: 3px solid var(--theme-primary) !important;
  box-shadow: 0 0 10px var(--theme-glow) !important;
}

/* ========================================
   PROTECTION DU BOUTON THÈME - COULEURS FIXES
   ======================================== */

/* Forcer le bouton thème à garder ses couleurs arc-en-ciel peu importe le thème */
.whatsapp-action-button.btn-theme,
button.whatsapp-action-button.btn-theme,
.whatsapp-actions .btn-theme,
.theme-default .whatsapp-action-button.btn-theme,
.theme-feminine .whatsapp-action-button.btn-theme,
.theme-masculine .whatsapp-action-button.btn-theme,
.theme-neutral .whatsapp-action-button.btn-theme {
  /* Forcer les couleurs arc-en-ciel TOUJOURS */
  background: linear-gradient(
    45deg,
    rgba(255, 0, 0, 0.6) 0%,
    /* Rouge intense */ rgba(255, 165, 0, 0.6) 14%,
    /* Orange intense */ rgba(255, 255, 0, 0.6) 28%,
    /* Jaune intense */ rgba(0, 255, 0, 0.6) 42%,
    /* Vert intense */ rgba(0, 255, 255, 0.6) 56%,
    /* Cyan intense */ rgba(0, 0, 255, 0.6) 70%,
    /* Bleu intense */ rgba(75, 0, 130, 0.6) 84%,
    /* Indigo intense */ rgba(238, 130, 238, 0.6) 100% /* Violet intense */
  ) !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  color: #ffffff !important;
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.6),
    inset 0 0 25px rgba(255, 255, 255, 0.3), 0 0 60px rgba(255, 0, 255, 0.4) !important;
  text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
  animation: gradientShift 3s ease-in-out infinite,
    pulseRainbow 2s ease-in-out infinite !important;
}

/* État de survol du bouton thème - TOUJOURS arc-en-ciel */
.whatsapp-action-button.btn-theme:hover,
button.whatsapp-action-button.btn-theme:hover,
.whatsapp-actions .btn-theme:hover,
.theme-default .whatsapp-action-button.btn-theme:hover,
.theme-feminine .whatsapp-action-button.btn-theme:hover,
.theme-masculine .whatsapp-action-button.btn-theme:hover,
.theme-neutral .whatsapp-action-button.btn-theme:hover {
  background: linear-gradient(
    45deg,
    rgba(255, 0, 0, 0.9) 0%,
    rgba(255, 165, 0, 0.9) 14%,
    rgba(255, 255, 0, 0.9) 28%,
    rgba(0, 255, 0, 0.9) 42%,
    rgba(0, 255, 255, 0.9) 56%,
    rgba(0, 0, 255, 0.9) 70%,
    rgba(75, 0, 130, 0.9) 84%,
    rgba(238, 130, 238, 0.9) 100%
  ) !important;
  transform: scale(1.25) translateY(-5px) rotateZ(5deg) !important;
  box-shadow: 0 0 60px rgba(255, 255, 255, 1),
    0 0 120px rgba(255, 255, 255, 0.6), inset 0 0 40px rgba(255, 255, 255, 0.5),
    0 0 100px rgba(255, 0, 255, 0.8), 0 15px 40px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(255, 255, 255, 1) !important;
  animation: rotateHalo 0.8s linear infinite,
    gradientShift 1.5s ease-in-out infinite,
    pulseRainbow 1s ease-in-out infinite !important;
  text-shadow: 0 0 30px rgba(255, 255, 255, 1),
    0 0 50px rgba(255, 255, 255, 0.8) !important;
}

/* ========================================
   BOUTONS SPÉCIALISÉS - COULEURS FIXES
   ======================================== */

/* Protéger les boutons spécialisés des changements de thème */
.btn-notifications,
.btn-status,
.btn-history,
.btn-stats,
.btn-voice-messages,
.btn-menu,
.btn-pinned,
.theme-default .btn-notifications,
.theme-feminine .btn-notifications,
.theme-masculine .btn-notifications,
.theme-neutral .btn-notifications,
.theme-default .btn-status,
.theme-feminine .btn-status,
.theme-masculine .btn-status,
.theme-neutral .btn-status,
.theme-default .btn-history,
.theme-feminine .btn-history,
.theme-masculine .btn-history,
.theme-neutral .btn-history,
.theme-default .btn-stats,
.theme-feminine .btn-stats,
.theme-masculine .btn-stats,
.theme-neutral .btn-stats,
.theme-default .btn-voice-messages,
.theme-feminine .btn-voice-messages,
.theme-masculine .btn-voice-messages,
.theme-neutral .btn-voice-messages,
.theme-default .btn-menu,
.theme-feminine .btn-menu,
.theme-masculine .btn-menu,
.theme-neutral .btn-menu,
.theme-default .btn-pinned,
.theme-feminine .btn-pinned,
.theme-masculine .btn-pinned,
.theme-neutral .btn-pinned {
  /* Garder leurs couleurs spécialisées originales */
  background: inherit !important;
  border-color: inherit !important;
  color: inherit !important;
  box-shadow: inherit !important;
}

/* ========================================
   PROTECTION DES BOUTONS D'OUTILS - COULEURS FIXES
   ======================================== */

/* Protéger les boutons d'outils (émojis et fichiers) des changements de thème */
.whatsapp-tool-button,
button.whatsapp-tool-button,
.whatsapp-input-tools .whatsapp-tool-button,
.theme-default .whatsapp-tool-button,
.theme-feminine .whatsapp-tool-button,
.theme-masculine .whatsapp-tool-button,
.theme-neutral .whatsapp-tool-button,
.theme-default button.whatsapp-tool-button,
.theme-feminine button.whatsapp-tool-button,
.theme-masculine button.whatsapp-tool-button,
.theme-neutral button.whatsapp-tool-button,
.theme-default .whatsapp-input-tools .whatsapp-tool-button,
.theme-feminine .whatsapp-input-tools .whatsapp-tool-button,
.theme-masculine .whatsapp-input-tools .whatsapp-tool-button,
.theme-neutral .whatsapp-input-tools .whatsapp-tool-button {
  /* Forcer les couleurs cyan-magenta-vert TOUJOURS */
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.3) 0%,
    rgba(255, 20, 147, 0.3) 50%,
    rgba(57, 255, 20, 0.3) 100%
  ) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  border: 2px solid rgba(0, 247, 255, 0.7) !important;
  color: #00f7ff !important;
  box-shadow: 0 0 25px rgba(0, 247, 255, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  text-shadow: 0 0 15px rgba(0, 247, 255, 0.9) !important;
}

/* État de survol des boutons d'outils - TOUJOURS cyan-magenta-vert */
.whatsapp-tool-button:hover,
button.whatsapp-tool-button:hover,
.whatsapp-input-tools .whatsapp-tool-button:hover,
.theme-default .whatsapp-tool-button:hover,
.theme-feminine .whatsapp-tool-button:hover,
.theme-masculine .whatsapp-tool-button:hover,
.theme-neutral .whatsapp-tool-button:hover,
.theme-default button.whatsapp-tool-button:hover,
.theme-feminine button.whatsapp-tool-button:hover,
.theme-masculine button.whatsapp-tool-button:hover,
.theme-neutral button.whatsapp-tool-button:hover,
.theme-default .whatsapp-input-tools .whatsapp-tool-button:hover,
.theme-feminine .whatsapp-input-tools .whatsapp-tool-button:hover,
.theme-masculine .whatsapp-input-tools .whatsapp-tool-button:hover,
.theme-neutral .whatsapp-input-tools .whatsapp-tool-button:hover {
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.5) 0%,
    rgba(255, 20, 147, 0.5) 50%,
    rgba(57, 255, 20, 0.5) 100%
  ) !important;
  transform: scale(1.25) translateY(-5px) !important;
  box-shadow: 0 0 50px rgba(0, 247, 255, 0.9), 0 0 100px rgba(0, 247, 255, 0.5),
    inset 0 0 30px rgba(255, 255, 255, 0.4), 0 15px 40px rgba(0, 0, 0, 0.3) !important;
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(0, 247, 255, 1) !important;
  border-color: rgba(0, 247, 255, 1) !important;
  animation: pulseGreen 0.8s ease-in-out infinite !important;
}

/* État actif du bouton emoji - TOUJOURS vert */
.whatsapp-tool-button.active,
button.whatsapp-tool-button.active,
.theme-default .whatsapp-tool-button.active,
.theme-feminine .whatsapp-tool-button.active,
.theme-masculine .whatsapp-tool-button.active,
.theme-neutral .whatsapp-tool-button.active,
.theme-default button.whatsapp-tool-button.active,
.theme-feminine button.whatsapp-tool-button.active,
.theme-masculine button.whatsapp-tool-button.active,
.theme-neutral button.whatsapp-tool-button.active {
  background: linear-gradient(
    135deg,
    rgba(57, 255, 20, 0.6) 0%,
    rgba(0, 255, 127, 0.6) 100%
  ) !important;
  border-color: rgba(57, 255, 20, 1) !important;
  color: #39ff14 !important;
  box-shadow: 0 0 35px rgba(57, 255, 20, 0.7),
    inset 0 0 25px rgba(57, 255, 20, 0.3) !important;
  text-shadow: 0 0 20px rgba(57, 255, 20, 1) !important;
  animation: pulseGreen 1.5s ease-in-out infinite !important;
}

/* ========================================
   PROTECTION DES BOUTONS D'ENVOI ET VOCAL - COULEURS FIXES
   ======================================== */

/* Protéger les boutons d'envoi et vocal des changements de thème */
.whatsapp-send-button,
.whatsapp-voice-button,
.theme-default .whatsapp-send-button,
.theme-feminine .whatsapp-send-button,
.theme-masculine .whatsapp-send-button,
.theme-neutral .whatsapp-send-button,
.theme-default .whatsapp-voice-button,
.theme-feminine .whatsapp-voice-button,
.theme-masculine .whatsapp-voice-button,
.theme-neutral .whatsapp-voice-button {
  /* Garder leurs couleurs spécialisées originales */
  background: inherit !important;
  border-color: inherit !important;
  color: inherit !important;
  box-shadow: inherit !important;
  text-shadow: inherit !important;
}

/* Bouton historique - Bleu électrique PRIORITAIRE */
.btn-history {
  border-color: rgba(30, 144, 255, 0.5) !important;
  color: #1e90ff !important;
  background: linear-gradient(
    135deg,
    rgba(30, 144, 255, 0.15) 0%,
    rgba(0, 191, 255, 0.15) 100%
  ) !important;
  box-shadow: 0 0 20px rgba(30, 144, 255, 0.4),
    inset 0 0 20px rgba(30, 144, 255, 0.1) !important;
}

.btn-history:hover {
  box-shadow: 0 0 40px rgba(30, 144, 255, 0.8), 0 0 80px rgba(30, 144, 255, 0.4) !important;
  color: #ffffff !important;
  text-shadow: 0 0 20px rgba(30, 144, 255, 1) !important;
  border-color: rgba(30, 144, 255, 1) !important;
  transform: scale(1.15) translateY(-3px) !important;
}

/* Bouton statistiques - Jaune électrique PRIORITAIRE */
.btn-stats {
  border-color: rgba(255, 215, 0, 0.5) !important;
  color: #ffd700 !important;
  background: linear-gradient(
    135deg,
    rgba(255, 215, 0, 0.15) 0%,
    rgba(255, 255, 0, 0.15) 100%
  ) !important;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.4),
    inset 0 0 20px rgba(255, 215, 0, 0.1) !important;
}

.btn-stats:hover {
  box-shadow: 0 0 40px rgba(255, 215, 0, 0.8), 0 0 80px rgba(255, 215, 0, 0.4) !important;
  color: #ffffff !important;
  text-shadow: 0 0 20px rgba(255, 215, 0, 1) !important;
  border-color: rgba(255, 215, 0, 1) !important;
  transform: scale(1.15) translateY(-3px) !important;
}

/* Bouton messages vocaux - Rose électrique PRIORITAIRE */
.btn-voice-messages {
  border-color: rgba(255, 20, 147, 0.5) !important;
  color: #ff1493 !important;
  background: linear-gradient(
    135deg,
    rgba(255, 20, 147, 0.15) 0%,
    rgba(255, 105, 180, 0.15) 100%
  ) !important;
  box-shadow: 0 0 20px rgba(255, 20, 147, 0.4),
    inset 0 0 20px rgba(255, 20, 147, 0.1) !important;
}

.btn-voice-messages:hover {
  box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 80px rgba(255, 20, 147, 0.4) !important;
  color: #ffffff !important;
  text-shadow: 0 0 20px rgba(255, 20, 147, 1) !important;
  border-color: rgba(255, 20, 147, 1) !important;
  transform: scale(1.15) translateY(-3px) !important;
}

/* Bouton menu - Cyan électrique PRIORITAIRE */
.btn-menu {
  border-color: rgba(0, 255, 255, 0.5) !important;
  color: #00ffff !important;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.15) 0%,
    rgba(0, 191, 255, 0.15) 100%
  ) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.4),
    inset 0 0 20px rgba(0, 255, 255, 0.1) !important;
}

.btn-menu:hover {
  box-shadow: 0 0 40px rgba(0, 255, 255, 0.8), 0 0 80px rgba(0, 255, 255, 0.4) !important;
  color: #ffffff !important;
  text-shadow: 0 0 20px rgba(0, 255, 255, 1) !important;
  border-color: rgba(0, 255, 255, 1) !important;
  transform: scale(1.15) translateY(-3px) !important;
}

/* STYLES PRIORITAIRES POUR BOUTONS D'ENVOI ET VOCAL */
.whatsapp-send-button {
  width: 46px !important;
  height: 46px !important;
  font-size: 1.2rem !important;
  min-width: 40px !important;
  min-height: 40px !important;
  /* Forcer le style vert néon laser */
  background: linear-gradient(
    135deg,
    rgba(57, 255, 20, 0.9) 0%,
    rgba(0, 255, 127, 0.9) 50%,
    rgba(34, 197, 94, 0.9) 100%
  ) !important;
  border: 2px solid rgba(57, 255, 20, 0.7) !important;
  color: #ffffff !important;
  box-shadow: 0 0 25px rgba(57, 255, 20, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

.whatsapp-send-button:hover {
  transform: scale(1.15) translateY(-3px) !important;
  box-shadow: 0 0 40px rgba(57, 255, 20, 0.8), 0 0 80px rgba(57, 255, 20, 0.4),
    0 10px 30px rgba(0, 0, 0, 0.2) !important;
  border-color: rgba(57, 255, 20, 1) !important;
  animation: pulseGreen 0.6s ease-in-out !important;
  text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
}

.whatsapp-voice-button {
  width: 46px !important;
  height: 46px !important;
  font-size: 1.1rem !important;
  min-width: 40px !important;
  min-height: 40px !important;
  /* Forcer le style plasma rouge-orange */
  background: linear-gradient(
    135deg,
    rgba(255, 69, 0, 0.9) 0%,
    rgba(255, 20, 147, 0.9) 50%,
    rgba(220, 38, 127, 0.9) 100%
  ) !important;
  border: 2px solid rgba(255, 69, 0, 0.7) !important;
  color: #ffffff !important;
  box-shadow: 0 0 25px rgba(255, 69, 0, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

.whatsapp-voice-button:hover {
  transform: scale(1.15) translateY(-3px) !important;
  box-shadow: 0 0 40px rgba(255, 69, 0, 0.8), 0 0 80px rgba(255, 20, 147, 0.4),
    0 10px 30px rgba(0, 0, 0, 0.2) !important;
  border-color: rgba(255, 69, 0, 1) !important;
  animation: pulseMagenta 0.6s ease-in-out !important;
  text-shadow: 0 0 15px rgba(255, 255, 255, 1) !important;
}

/* ========================================
   BOUTONS D'OUTILS - ÉMOJIS ET FICHIERS ULTRA PRIORITAIRES
   ======================================== */

/* Forcer l'affichage des boutons d'outils avec spécificité maximale */
.whatsapp-tool-button,
button.whatsapp-tool-button,
.whatsapp-input-tools .whatsapp-tool-button {
  width: var(--tool-button-size, 42px) !important;
  height: var(--tool-button-size, 42px) !important;
  font-size: var(--font-tiny, 1.2rem) !important;
  min-width: 38px !important;
  min-height: 38px !important;

  /* Forcer la visibilité */
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;

  /* Couleurs fluorescentes ultra-visibles */
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.3) 0%,
    rgba(255, 20, 147, 0.3) 50%,
    rgba(57, 255, 20, 0.3) 100%
  ) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  border: 2px solid rgba(0, 247, 255, 0.7) !important;
  color: #00f7ff !important;

  /* Layout garanti */
  border-radius: 50% !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  z-index: 10 !important;

  /* Effets lumineux garantis */
  box-shadow: 0 0 25px rgba(0, 247, 255, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
  text-shadow: 0 0 15px rgba(0, 247, 255, 0.9) !important;
}

/* État de survol ultra-intense pour boutons d'outils */
.whatsapp-tool-button:hover,
button.whatsapp-tool-button:hover,
.whatsapp-input-tools .whatsapp-tool-button:hover {
  transform: scale(1.25) translateY(-5px) !important;

  /* Lueurs ultra-intenses */
  box-shadow: 0 0 50px rgba(0, 247, 255, 0.9), 0 0 100px rgba(0, 247, 255, 0.5),
    inset 0 0 30px rgba(255, 255, 255, 0.4), 0 15px 40px rgba(0, 0, 0, 0.3) !important;

  /* Couleurs renforcées */
  color: #ffffff !important;
  text-shadow: 0 0 25px rgba(0, 247, 255, 1) !important;
  border-color: rgba(0, 247, 255, 1) !important;

  /* Background plus intense */
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.5) 0%,
    rgba(255, 20, 147, 0.5) 50%,
    rgba(57, 255, 20, 0.5) 100%
  ) !important;

  /* Animation pulsante */
  animation: pulseGreen 0.8s ease-in-out infinite !important;
}

/* État actif pour le bouton emoji */
.whatsapp-tool-button.active,
button.whatsapp-tool-button.active {
  background: linear-gradient(
    135deg,
    rgba(57, 255, 20, 0.6) 0%,
    rgba(0, 255, 127, 0.6) 100%
  ) !important;
  border-color: rgba(57, 255, 20, 1) !important;
  color: #39ff14 !important;
  box-shadow: 0 0 35px rgba(57, 255, 20, 0.7),
    inset 0 0 25px rgba(57, 255, 20, 0.3) !important;
  text-shadow: 0 0 20px rgba(57, 255, 20, 1) !important;
  animation: pulseGreen 1.5s ease-in-out infinite !important;
}

.whatsapp-avatar {
  width: var(--avatar-size, 40px) !important;
  height: var(--avatar-size, 40px) !important;
  min-width: 32px !important;
  min-height: 32px !important;
}

.whatsapp-username {
  font-size: var(--font-base, 1rem) !important;
  min-font-size: 0.8rem !important;
}

.whatsapp-status {
  font-size: var(--font-tiny, 0.75rem) !important;
  min-font-size: 0.6rem !important;
}

.whatsapp-input {
  font-size: var(--font-base, 1rem) !important;
  padding: var(--spacing-small, 0.25rem) var(--spacing-base, 0.5rem) !important;
  min-font-size: 0.8rem !important;
}

.futuristic-messages-container {
  max-height: calc(
    100vh - var(--header-height, 52px) - var(--input-height, 52px) - 20px
  ) !important;
  min-height: calc(100vh - 140px) !important;
  padding: var(--spacing-base, 0.5rem) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Optimisations pour différents niveaux de zoom - RÉDUCTION DES TAILLES */
@media (min-zoom: 1.25),
  (min--moz-device-pixel-ratio: 1.25),
  (min-device-pixel-ratio: 1.25) {
  .whatsapp-chat-header {
    height: 36px !important;
    padding: 2px 4px !important;
  }

  .whatsapp-input-container {
    min-height: 36px !important;
    padding: 3px 4px !important;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 90px) !important;
    padding: 0.3rem !important;
  }

  .whatsapp-action-button {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.7rem !important;
  }

  .whatsapp-send-button,
  .whatsapp-voice-button {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.75rem !important;
  }

  .whatsapp-tool-button {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.65rem !important;
  }

  .whatsapp-avatar {
    width: 28px !important;
    height: 28px !important;
  }

  .whatsapp-username {
    font-size: 0.8rem !important;
  }

  .whatsapp-status {
    font-size: 0.65rem !important;
  }
}

@media (min-zoom: 1.5),
  (min--moz-device-pixel-ratio: 1.5),
  (min-device-pixel-ratio: 1.5) {
  .whatsapp-chat-header {
    height: 32px !important;
    padding: 1px 3px !important;
  }

  .whatsapp-input-container {
    min-height: 32px !important;
    padding: 2px 3px !important;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 80px) !important;
    padding: 0.2rem !important;
  }

  .whatsapp-action-button {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.6rem !important;
  }

  .whatsapp-send-button,
  .whatsapp-voice-button {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.65rem !important;
  }

  .whatsapp-tool-button {
    width: 20px !important;
    height: 20px !important;
    font-size: 0.55rem !important;
  }

  .whatsapp-avatar {
    width: 24px !important;
    height: 24px !important;
  }

  .whatsapp-username {
    font-size: 0.7rem !important;
  }

  .whatsapp-status {
    font-size: 0.55rem !important;
  }
}

@media (min-zoom: 2),
  (min--moz-device-pixel-ratio: 2),
  (min-device-pixel-ratio: 2) {
  .whatsapp-chat-header {
    height: 28px !important;
    padding: 1px 2px !important;
  }

  .whatsapp-input-container {
    min-height: 28px !important;
    padding: 1px 2px !important;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 70px) !important;
    padding: 0.1rem !important;
  }

  .whatsapp-action-button {
    width: 20px !important;
    height: 20px !important;
    font-size: 0.5rem !important;
  }

  .whatsapp-send-button,
  .whatsapp-voice-button {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.55rem !important;
  }

  .whatsapp-tool-button {
    width: 18px !important;
    height: 18px !important;
    font-size: 0.45rem !important;
  }

  .whatsapp-avatar {
    width: 20px !important;
    height: 20px !important;
  }

  .whatsapp-username {
    font-size: 0.6rem !important;
  }

  .whatsapp-status {
    font-size: 0.45rem !important;
  }

  .whatsapp-input {
    font-size: 0.7rem !important;
    padding: 2px 4px !important;
  }

  /* Badges plus petits pour zoom élevé */
  .whatsapp-action-button .absolute.-top-1.-right-1 {
    min-width: 12px !important;
    height: 12px !important;
    font-size: 7px !important;
    padding: 0 2px !important;
  }

  .notification-badge {
    padding: 1px 3px !important;
    font-size: 7px !important;
    min-width: 14px !important;
  }

  .online-count-badge {
    padding: 1px 4px !important;
    font-size: 7px !important;
  }

  .notification-icon {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.7rem !important;
  }

  /* Messages plus compacts */
  .futuristic-message {
    margin-bottom: 0.2rem !important;
    max-width: 90% !important;
  }

  .futuristic-message-bubble {
    padding: 4px 6px !important;
    font-size: 0.7rem !important;
  }

  .voice-message-modern {
    min-width: 120px !important;
    max-width: 180px !important;
    padding: 2px 4px !important;
  }
}

/* Optimisations pour fenêtres réduites - SUPPRIMÉ (redondant avec tablettes) */

/* Optimisations pour hauteur réduite (fenêtre horizontale) */
@media (max-height: 600px) {
  .whatsapp-chat-header {
    height: 44px !important;
    padding: 3px 6px !important;
  }

  .whatsapp-input-container {
    min-height: 44px !important;
    padding: 4px 6px !important;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 110px) !important;
    padding: 0.4rem !important;
  }

  .whatsapp-avatar {
    width: 32px !important;
    height: 32px !important;
  }

  .whatsapp-action-button {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.85rem !important;
  }

  .whatsapp-send-button,
  .whatsapp-voice-button {
    width: 36px !important;
    height: 36px !important;
    font-size: 0.9rem !important;
  }
}

@media (max-height: 500px) {
  .whatsapp-chat-header {
    height: 40px !important;
    padding: 2px 4px !important;
  }

  .whatsapp-input-container {
    min-height: 40px !important;
    padding: 3px 4px !important;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 100px) !important;
    padding: 0.3rem !important;
  }

  .whatsapp-avatar {
    width: 28px !important;
    height: 28px !important;
  }

  .whatsapp-action-button {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.8rem !important;
  }

  .whatsapp-send-button,
  .whatsapp-voice-button {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.85rem !important;
  }

  .whatsapp-tool-button {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.75rem !important;
  }
}

/* Optimisations pour écrans en mode paysage sur mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .whatsapp-chat-header {
    height: 36px !important;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 90px) !important;
  }

  .whatsapp-input-container {
    min-height: 36px !important;
  }

  .voice-message-modern {
    padding: 4px 6px !important;
  }

  /* Badges responsive pour écrans réduits */
  .whatsapp-action-button .absolute.-top-1.-right-1 {
    min-width: 16px !important;
    height: 16px !important;
    font-size: 9px !important;
    padding: 0 3px !important;
  }

  .notification-badge {
    padding: 3px 6px !important;
    font-size: 10px !important;
    min-width: 20px !important;
  }

  .online-count-badge {
    padding: 3px 8px !important;
    font-size: 10px !important;
  }

  .notification-icon {
    width: 36px !important;
    height: 36px !important;
    font-size: 1rem !important;
  }
}

/* Optimisations spéciales pour très petites fenêtres */
@media (max-width: 600px) and (max-height: 400px) {
  .whatsapp-chat-header {
    height: 32px !important;
    padding: 1px 3px !important;
  }

  .whatsapp-input-container {
    min-height: 32px !important;
    padding: 2px 3px !important;
  }

  .futuristic-messages-container {
    max-height: calc(100vh - 80px) !important;
    padding: 0.2rem !important;
  }

  .whatsapp-avatar {
    width: 24px !important;
    height: 24px !important;
  }

  .whatsapp-username {
    font-size: 0.75rem !important;
  }

  .whatsapp-status {
    font-size: 0.65rem !important;
  }

  .whatsapp-action-button {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.7rem !important;
  }

  .whatsapp-send-button,
  .whatsapp-voice-button {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.75rem !important;
  }

  .whatsapp-tool-button {
    width: 20px !important;
    height: 20px !important;
    font-size: 0.7rem !important;
  }
}

/* ========================================
   CORRECTION DE L'AFFICHAGE ET MISE EN PAGE
   ======================================== */

/* Correction du conteneur principal */
.whatsapp-chat-container {
  display: flex !important;
  flex-direction: column !important;
  height: 100vh !important;
  max-height: 100vh !important;
  overflow: hidden !important;
  position: relative !important;
}

/* Correction de l'en-tête */
.whatsapp-chat-header {
  flex-shrink: 0 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 100 !important;
  background: var(--modern-white) !important;
  backdrop-filter: var(--blur-effect) !important;
  border-bottom: 1px solid var(--modern-gray-200) !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--dark-surface) !important;
  border-bottom: 1px solid var(--dark-accent) !important;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3) !important;
}

/* ========================================
   DÉFINITIONS CONSOLIDÉES - NETTOYAGE DES DUPLICATIONS
   ======================================== */

/* Conteneur de messages moderne - DÉFINITION CONSOLIDÉE */
.futuristic-messages-container {
  flex: 1 !important;
  padding: 0.75rem !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  scroll-behavior: smooth !important;
  max-height: calc(100vh - 180px) !important;
  backdrop-filter: var(--blur-effect) !important;
  scrollbar-width: thin !important;
  scrollbar-color: var(--modern-gray-400) transparent !important;
  -webkit-overflow-scrolling: touch !important;
  position: relative !important;

  /* Fond moderne pour mode clair */
  background: var(--modern-light);
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(102, 126, 234, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(245, 87, 108, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 50% 50%,
      rgba(79, 172, 254, 0.05) 0%,
      transparent 50%
    ),
    linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.02) 0%,
      rgba(118, 75, 162, 0.02) 100%
    );
}

/* Zone de saisie moderne - DÉFINITION CONSOLIDÉE */
.whatsapp-input-container {
  padding: 12px 16px !important;
  min-height: 60px !important;
  flex-shrink: 0 !important;
  position: sticky !important;
  bottom: 0 !important;
  z-index: 100 !important;
  backdrop-filter: var(--blur-effect) !important;
  border-top: 1px solid var(--modern-gray-200) !important;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05) !important;

  /* Fond moderne pour mode clair */
  background: var(--modern-white);
  background-image: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.03) 0%,
      rgba(245, 87, 108, 0.03) 100%
    ),
    radial-gradient(
      circle at 30% 70%,
      rgba(79, 172, 254, 0.05) 0%,
      transparent 50%
    );
}

:host-context(.dark) .whatsapp-input-container {
  background: var(--dark-surface) !important;
  border-top: 1px solid var(--dark-accent) !important;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.3) !important;
}

/* Correction des boutons d'action */
.whatsapp-action-button,
.whatsapp-send-button,
.whatsapp-voice-button,
.whatsapp-tool-button {
  flex-shrink: 0 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Correction du champ de texte */
.whatsapp-input {
  flex: 1 !important;
  min-width: 0 !important;
  resize: none !important;
  overflow-y: auto !important;
  max-height: 120px !important;
  border: none !important;
  outline: none !important;
}

/* Correction des conteneurs flexibles */
.whatsapp-input-container > div {
  display: flex !important;
  align-items: flex-end !important;
  gap: 0.5rem !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
}

/* ========================================
   OPTIMISATIONS POUR ZONE DE SAISIE ET MESSAGES
   ======================================== */

/* Optimisations spécifiques pour les messages */
.futuristic-message {
  margin-bottom: 0.5rem !important;
  max-width: 85% !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.futuristic-message-bubble {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  padding: 8px 12px !important;
  border-radius: 12px !important;
}

/* Optimisations pour les images dans les messages */
.futuristic-message img {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 8px !important;
  object-fit: cover !important;
}

/* Optimisations pour les messages vocaux */
.voice-message-modern {
  min-width: 200px !important;
  max-width: 300px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Correction globale pour éviter les débordements */
* {
  box-sizing: border-box !important;
}

/* Correction pour les badges et notifications */
.notification-badge,
.online-count-badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 18px !important;
  height: 18px !important;
  border-radius: 9px !important;
  font-size: 10px !important;
  font-weight: 600 !important;
  line-height: 1 !important;
  padding: 0 4px !important;
}

/* Correction pour les icônes */
.notification-icon,
.whatsapp-action-button i,
.whatsapp-send-button i,
.whatsapp-voice-button i,
.whatsapp-tool-button i {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
}

/* Optimisations pour les très petits écrans */
@media (max-width: 480px) {
  .futuristic-message {
    max-width: 90% !important;
  }

  .voice-message-modern {
    min-width: 150px !important;
    max-width: 250px !important;
  }

  .whatsapp-input {
    font-size: 14px !important;
  }

  .whatsapp-username {
    font-size: 0.85rem !important;
  }

  .whatsapp-status {
    font-size: 0.7rem !important;
  }
}

/* Optimisations pour les écrans tactiles */
@media (pointer: coarse) {
  .whatsapp-action-button,
  .whatsapp-send-button,
  .whatsapp-voice-button {
    min-width: 44px !important;
    min-height: 44px !important;
  }

  .whatsapp-tool-button {
    min-width: 36px !important;
    min-height: 36px !important;
  }
}

/* Optimisations pour l'accessibilité */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimisations pour le contraste élevé */
@media (prefers-contrast: high) {
  .whatsapp-chat-header,
  .whatsapp-input-container {
    border-width: 2px !important;
  }

  .whatsapp-action-button,
  .whatsapp-send-button,
  .whatsapp-voice-button,
  .whatsapp-tool-button {
    border: 2px solid currentColor !important;
  }
}

/* ========================================
   CORRECTIONS FINALES POUR STABILITÉ
   ======================================== */

/* Force la stabilité du layout */
.whatsapp-chat-container {
  position: relative !important;
  width: 100% !important;
  height: 100vh !important;
  max-height: 100vh !important;
  min-height: 100vh !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Définitions de layout redondantes - SUPPRIMÉES (déjà définies dans les sections consolidées) */

/* Correction pour éviter les conflits de z-index */
.whatsapp-chat-header,
.whatsapp-input-container {
  isolation: isolate !important;
}

/* Correction pour les éléments flottants */
.whatsapp-action-button,
.whatsapp-send-button,
.whatsapp-voice-button,
.whatsapp-tool-button {
  position: relative !important;
  z-index: 1 !important;
}

/* Assure la cohérence des tailles minimales */
.whatsapp-chat-header {
  min-height: 50px !important;
}

.whatsapp-input-container {
  min-height: 50px !important;
}

/* Correction pour les navigateurs WebKit */
@supports (-webkit-appearance: none) {
  .whatsapp-chat-container {
    -webkit-overflow-scrolling: touch !important;
  }

  .futuristic-messages-container {
    -webkit-overflow-scrolling: touch !important;
  }
}

/* Correction pour Firefox */
@-moz-document url-prefix() {
  .whatsapp-chat-container {
    scrollbar-width: thin !important;
  }

  .futuristic-messages-container {
    scrollbar-width: thin !important;
  }
}
